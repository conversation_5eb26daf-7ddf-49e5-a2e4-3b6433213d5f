# -*- coding: utf-8 -*-
"""
utils 包
--------
包含NetCDF文件读取所需的辅助工具模块。
"""

# 仅导出最基本和常用的模块，避免循环导入和重依赖问题
__all__ = [
    'CustomLogger',
    'update_config_dates',
    'load_yaml_config',
    'get_config_path',
    'get_data_path',
    'get_output_path',
    'get_log_path'
]

# 延迟导入，只在需要时导入
def __getattr__(name):
    if name == 'CustomLogger':
        from .custom_logger import CustomLogger
        return CustomLogger
    elif name == 'update_config_dates':
        from .config_manager import update_config_dates
        return update_config_dates
    elif name == 'load_yaml_config':
        from .config_manager import load_yaml_config
        return load_yaml_config
    elif name in ['get_config_path', 'get_data_path', 'get_output_path', 'get_log_path']:
        from .path_helper import get_config_path, get_data_path, get_output_path, get_log_path
        return locals()[name]
    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
