name: 10_metre_wind_component
description: 10米风分量合并数据三维处理配置（包含u10和v10变量）
data:
  date_folder: '2025_07_29'
  input_file: ./data/2025_07_29/10_metre_wind_component/10_metre_wind_component.nc
  data_type: netcdf
  variables:
    u_component: u10
    v_component: v10
  multilayer:
    enabled: true
    process_all_timesteps: true
    time_range:
    - 0
    - 6
    time_labels:
    - 24
    - 48
    - 72
    - 96
    - 120
    - 144
    - 168
processing:
  unit_conversion: 1.0
  valid_range:
  - -50.0
  - 50.0
  fill_value: nan
  filter:
    enabled: false
    type: gaussian
    parameters:
      sigma: 0.5
output:
  base_path: ./output/2025_07_29/
  crs: EPSG:4326
  include_value: true
  longitude_range: '0_360'
  convert_to_180: false
  format: json
  separate_timesteps: true
  output_pattern: wind_ecmwf_10m_{time_label}.json
  intermediate_files:
    u_pattern: wind_ecmwf_u10_{time_label}.json
    v_pattern: wind_ecmwf_v10_{time_label}.json
netcdf:
  coordinate_variables:
    latitude:
    - lat
    - latitude
    - y
    - lat_0
    longitude:
    - lon
    - longitude
    - x
    - lon_0
    time:
    - time
    - forecast_time
  data_processing:
    flatten_method: row_major
    handle_nan: true
    nan_replacement: null
  metadata:
    u_component:
      parameter_number: 2
      parameter_name: U-component_of_wind
      parameter_unit: m.s-1
      surface_type: 103
      surface_value: 10.0
    v_component:
      parameter_number: 3
      parameter_name: V-component_of_wind
      parameter_unit: m.s-1
      surface_type: 103
      surface_value: 10.0
wind_layer:
  header_template:
    discipline: 0
    disciplineName: Meteorological Products
    gribEdition: 2
    center: 98
    centerName: European Centre for Medium-Range Weather Forecasts
    parameterCategory: 2
    parameterUnit: m.s-1
    surface1Type: 103
    surface1TypeName: Specified height level above ground
    surface1Value: 10.0
  forecast_times:
  - 24
  - 48
  - 72
  - 96
  - 120
  - 144
  - 168
  model_type: ecmwf
  combined_variables: true
geometry_simplification:
  enabled: false
  method: none
  parameters:
    tolerance: 0
