[2025-07-29 14:16:17] === nc_to_geojson.py 开始执行 ===
[2025-07-29 14:16:17] [INFO] 日志文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\log\nc_to_geojson_202507291416.log
[2025-07-29 14:16:17] [INFO] 运行模式: 全部处理
[2025-07-29 14:16:17] 
=== 步骤 1: 更新配置文件日期 ===
[2025-07-29 14:16:17] [INFO] 开始更新配置文件日期...
[2025-07-29 14:16:17] [SUCCESS] 配置文件日期更新成功，目标日期: 2025_07_28
[2025-07-29 14:16:17] [INFO] 成功更新 5/5 个配置文件
[2025-07-29 14:16:17] 
=== 步骤 2: 处理ECMWF数据 ===
[2025-07-29 14:16:17] [INFO] 处理配置文件: 10_metre_wind_component.yaml (ECMWF风场数据)
[2025-07-29 14:16:17] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\config\10_metre_wind_component.yaml
[2025-07-29 14:16:17] [INFO] 开始处理合并的风分量数据
[2025-07-29 14:16:17] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\10_metre_wind_component\10_metre_wind_component.nc
[2025-07-29 14:16:17] [INFO] 需要读取的变量: ['longitude', 'latitude', 'u10', 'v10', 'valid_time']
[2025-07-29 14:16:17] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\10_metre_wind_component\10_metre_wind_component.nc
[2025-07-29 14:16:17] [SUCCESS] NetCDF文件数据读取成功
[2025-07-29 14:16:17] [INFO] 数据维度 - 经度: 1440, 纬度: 721
[2025-07-29 14:16:17] [INFO] 时间步: 7
[2025-07-29 14:16:17] [INFO] 使用配置的base_path: ./output/2025_07_28/
[2025-07-29 14:16:17] [INFO] 输出目录: output\2025_07_28
[2025-07-29 14:16:17] [INFO] 输出前缀: wind_ecmwf_10m
[2025-07-29 14:16:17] [INFO] 预报时间列表: [24, 48, 72, 96, 120, 144, 168]
[2025-07-29 14:16:17] [INFO] 处理时间步 1/7
[2025-07-29 14:16:17] [INFO] 时间: 2025-07-23T12:00:00Z
[2025-07-29 14:16:24] [INFO] 计算的边界框: [0.0, -90.0, 359.75, 90.0]
[2025-07-29 14:16:26] [SUCCESS] 已生成文件: wind_ecmwf_10m_24.json (包含 677473 个数据点)
[2025-07-29 14:16:26] [INFO] 处理时间步 2/7
[2025-07-29 14:16:26] [INFO] 时间: 2025-07-24T12:00:00Z
[2025-07-29 14:16:37] [INFO] 计算的边界框: [0.0, -90.0, 359.75, 90.0]
[2025-07-29 14:16:39] [SUCCESS] 已生成文件: wind_ecmwf_10m_48.json (包含 677473 个数据点)
[2025-07-29 14:16:39] [INFO] 处理时间步 3/7
[2025-07-29 14:16:39] [INFO] 时间: 2025-07-25T12:00:00Z
[2025-07-29 14:17:01] [INFO] 计算的边界框: [0.0, -90.0, 359.75, 90.0]
[2025-07-29 14:17:03] [SUCCESS] 已生成文件: wind_ecmwf_10m_72.json (包含 677473 个数据点)
[2025-07-29 14:17:03] [INFO] 处理时间步 4/7
[2025-07-29 14:17:03] [INFO] 时间: 2025-07-26T12:00:00Z
[2025-07-29 14:17:15] [INFO] 计算的边界框: [0.0, -90.0, 359.75, 90.0]
[2025-07-29 14:17:17] [SUCCESS] 已生成文件: wind_ecmwf_10m_96.json (包含 677473 个数据点)
[2025-07-29 14:17:17] [INFO] 处理时间步 5/7
[2025-07-29 14:17:17] [INFO] 时间: 2025-07-27T12:00:00Z
[2025-07-29 14:17:25] [INFO] 计算的边界框: [0.0, -90.0, 359.75, 90.0]
[2025-07-29 14:17:26] [SUCCESS] 已生成文件: wind_ecmwf_10m_120.json (包含 677473 个数据点)
[2025-07-29 14:17:26] [INFO] 处理时间步 6/7
[2025-07-29 14:17:26] [INFO] 时间: 2025-07-28T12:00:00Z
[2025-07-29 14:17:35] [INFO] 计算的边界框: [0.0, -90.0, 359.75, 90.0]
[2025-07-29 14:17:37] [SUCCESS] 已生成文件: wind_ecmwf_10m_144.json (包含 677473 个数据点)
[2025-07-29 14:17:37] [INFO] 处理时间步 7/7
[2025-07-29 14:17:37] [INFO] 时间: 2025-07-29T12:00:00Z
[2025-07-29 14:17:45] [INFO] 计算的边界框: [0.0, -90.0, 359.75, 90.0]
[2025-07-29 14:17:46] [SUCCESS] 已生成文件: wind_ecmwf_10m_168.json (包含 677473 个数据点)
[2025-07-29 14:17:47] [SUCCESS] ECMWF风场数据处理完成，生成 7 个文件
[2025-07-29 14:17:47] 
=== 步骤 3: 处理GFS数据 ===
[2025-07-29 14:17:47] [INFO] 处理配置文件: component_of_wind.yaml (合并风分量数据)
[2025-07-29 14:17:47] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\config\component_of_wind.yaml
[2025-07-29 14:17:47] [INFO] 开始处理合并的风分量数据
[2025-07-29 14:17:47] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\U_component_of_wind\U_component_of_wind.nc
[2025-07-29 14:17:47] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\V_component_of_wind\V_component_of_wind.nc
[2025-07-29 14:17:47] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\U_component_of_wind\U_component_of_wind.nc
[2025-07-29 14:17:47] [SUCCESS] NetCDF文件数据读取成功
[2025-07-29 14:17:47] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\V_component_of_wind\V_component_of_wind.nc
[2025-07-29 14:17:47] [SUCCESS] NetCDF文件数据读取成功
[2025-07-29 14:17:47] [INFO] 数据维度 - 经度: 1440, 纬度: 721, 时间步: 7
[2025-07-29 14:17:47] [INFO] 处理时间步 1/7
[2025-07-29 14:17:47] [INFO] 时间: 2025-07-23T12:00:00Z
[2025-07-29 14:17:55] [INFO] 计算的边界框: [0.0, -90.0, 359.75, 90.0]
[2025-07-29 14:17:56] [SUCCESS] 已生成文件: wind_gfs_10m_24.json (包含 677473 个数据点)
[2025-07-29 14:17:56] [INFO] 处理时间步 2/7
[2025-07-29 14:17:56] [INFO] 时间: 2025-07-24T12:00:00Z
[2025-07-29 14:18:05] [INFO] 计算的边界框: [0.0, -90.0, 359.75, 90.0]
[2025-07-29 14:18:06] [SUCCESS] 已生成文件: wind_gfs_10m_48.json (包含 677473 个数据点)
[2025-07-29 14:18:06] [INFO] 处理时间步 3/7
[2025-07-29 14:18:06] [INFO] 时间: 2025-07-25T12:00:00Z
[2025-07-29 14:18:15] [INFO] 计算的边界框: [0.0, -90.0, 359.75, 90.0]
[2025-07-29 14:18:17] [SUCCESS] 已生成文件: wind_gfs_10m_72.json (包含 677473 个数据点)
[2025-07-29 14:18:17] [INFO] 处理时间步 4/7
[2025-07-29 14:18:17] [INFO] 时间: 2025-07-26T12:00:00Z
[2025-07-29 14:18:25] [INFO] 计算的边界框: [0.0, -90.0, 359.75, 90.0]
[2025-07-29 14:18:26] [SUCCESS] 已生成文件: wind_gfs_10m_96.json (包含 677473 个数据点)
[2025-07-29 14:18:26] [INFO] 处理时间步 5/7
[2025-07-29 14:18:26] [INFO] 时间: 2025-07-27T12:00:00Z
[2025-07-29 14:18:35] [INFO] 计算的边界框: [0.0, -90.0, 359.75, 90.0]
[2025-07-29 14:18:36] [SUCCESS] 已生成文件: wind_gfs_10m_120.json (包含 677473 个数据点)
[2025-07-29 14:18:36] [INFO] 处理时间步 6/7
[2025-07-29 14:18:36] [INFO] 时间: 2025-07-28T12:00:00Z
[2025-07-29 14:18:44] [INFO] 计算的边界框: [0.0, -90.0, 359.75, 90.0]
[2025-07-29 14:18:46] [SUCCESS] 已生成文件: wind_gfs_10m_144.json (包含 677473 个数据点)
[2025-07-29 14:18:46] [INFO] 处理时间步 7/7
[2025-07-29 14:18:46] [INFO] 时间: 2025-07-29T12:00:00Z
[2025-07-29 14:18:55] [INFO] 计算的边界框: [0.0, -90.0, 359.75, 90.0]
[2025-07-29 14:18:56] [SUCCESS] 已生成文件: wind_gfs_10m_168.json (包含 677473 个数据点)
[2025-07-29 14:18:56] [SUCCESS] 合并风分量数据处理完成，生成 7 个文件
[2025-07-29 14:18:56] 
=== 步骤 4: 处理地转流场数据 ===
[2025-07-29 14:18:56] [INFO] 处理配置文件: depth_current.yaml (合并海流数据)
[2025-07-29 14:18:56] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\config\depth_current.yaml
[2025-07-29 14:18:56] [INFO] 开始处理合并的海流深度数据
[2025-07-29 14:18:56] [INFO] 正在读取 eastward_deep 数据
[2025-07-29 14:18:56] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\eastward_current_deep\eastward_current_deep.nc
[2025-07-29 14:18:56] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\eastward_current_deep\eastward_current_deep.nc
[2025-07-29 14:18:57] [SUCCESS] NetCDF文件数据读取成功
[2025-07-29 14:18:57] [INFO] 正在读取 northward_deep 数据
[2025-07-29 14:18:57] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\northward_current_deep\northward_current_deep.nc
[2025-07-29 14:18:57] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\northward_current_deep\northward_current_deep.nc
[2025-07-29 14:18:57] [SUCCESS] NetCDF文件数据读取成功
[2025-07-29 14:18:57] [INFO] 正在读取 eastward_shallow 数据
[2025-07-29 14:18:57] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\eastward_current_shallow\eastward_current_shallow.nc
[2025-07-29 14:18:57] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\eastward_current_shallow\eastward_current_shallow.nc
[2025-07-29 14:18:58] [SUCCESS] NetCDF文件数据读取成功
[2025-07-29 14:18:58] [INFO] 正在读取 northward_shallow 数据
[2025-07-29 14:18:58] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\northward_current_shallow\northward_current_shallow.nc
[2025-07-29 14:18:58] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\northward_current_shallow\northward_current_shallow.nc
[2025-07-29 14:18:58] [SUCCESS] NetCDF文件数据读取成功
[2025-07-29 14:18:58] [INFO] 处理deep层海流数据...
[2025-07-29 14:18:58] [INFO] 数据维度 - 经度: 1801, 纬度: 721, 深度: 11
[2025-07-29 14:18:58] [INFO] 原始海流数据维度 - uo: (1, 11, 721, 1801), vo: (1, 11, 721, 1801)
[2025-07-29 14:18:58] [INFO] 开始降采样海流数据...
[2025-07-29 14:18:58] [INFO] 降采样: (1, 11, 721, 1801) -> (1, 11, 241, 601)
[2025-07-29 14:18:58] [INFO] 降采样: (1, 11, 721, 1801) -> (1, 11, 241, 601)
[2025-07-29 14:18:58] [INFO] 处理深度层 1/11 - 77.85385131835938m (标签: 78)
[2025-07-29 14:19:00] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:00] [SUCCESS] 已生成文件: depth_current_78.json (包含 132242 个数据点)
[2025-07-29 14:19:00] [INFO] 处理深度层 2/11 - 92.3260726928711m (标签: 92)
[2025-07-29 14:19:01] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:01] [SUCCESS] 已生成文件: depth_current_92.json (包含 132098 个数据点)
[2025-07-29 14:19:01] [INFO] 处理深度层 3/11 - 109.72930145263672m (标签: 110)
[2025-07-29 14:19:03] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:03] [SUCCESS] 已生成文件: depth_current_110.json (包含 131940 个数据点)
[2025-07-29 14:19:03] [INFO] 处理深度层 4/11 - 130.66600036621094m (标签: 131)
[2025-07-29 14:19:04] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:05] [SUCCESS] 已生成文件: depth_current_131.json (包含 131799 个数据点)
[2025-07-29 14:19:05] [INFO] 处理深度层 5/11 - 155.85069274902344m (标签: 156)
[2025-07-29 14:19:06] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:06] [SUCCESS] 已生成文件: depth_current_156.json (包含 131646 个数据点)
[2025-07-29 14:19:06] [INFO] 处理深度层 6/11 - 186.12559509277344m (标签: 186)
[2025-07-29 14:19:08] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:08] [SUCCESS] 已生成文件: depth_current_186.json (包含 131516 个数据点)
[2025-07-29 14:19:08] [INFO] 处理深度层 7/11 - 222.47520446777344m (标签: 222)
[2025-07-29 14:19:09] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:09] [SUCCESS] 已生成文件: depth_current_222.json (包含 131372 个数据点)
[2025-07-29 14:19:09] [INFO] 处理深度层 8/11 - 266.0403137207031m (标签: 266)
[2025-07-29 14:19:11] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:11] [SUCCESS] 已生成文件: depth_current_266.json (包含 131273 个数据点)
[2025-07-29 14:19:11] [INFO] 处理深度层 9/11 - 318.1274108886719m (标签: 318)
[2025-07-29 14:19:13] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:13] [SUCCESS] 已生成文件: depth_current_318.json (包含 131184 个数据点)
[2025-07-29 14:19:13] [INFO] 处理深度层 10/11 - 380.2130126953125m (标签: 380)
[2025-07-29 14:19:14] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:14] [SUCCESS] 已生成文件: depth_current_380.json (包含 131090 个数据点)
[2025-07-29 14:19:14] [INFO] 处理深度层 11/11 - 453.9377136230469m (标签: 454)
[2025-07-29 14:19:16] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:16] [SUCCESS] 已生成文件: depth_current_454.json (包含 130948 个数据点)
[2025-07-29 14:19:16] [INFO] 处理shallow层海流数据...
[2025-07-29 14:19:16] [INFO] 数据维度 - 经度: 1801, 纬度: 721, 深度: 21
[2025-07-29 14:19:16] [INFO] 原始海流数据维度 - uo: (1, 21, 721, 1801), vo: (1, 21, 721, 1801)
[2025-07-29 14:19:16] [INFO] 开始降采样海流数据...
[2025-07-29 14:19:16] [INFO] 降采样: (1, 21, 721, 1801) -> (1, 21, 241, 601)
[2025-07-29 14:19:16] [INFO] 降采样: (1, 21, 721, 1801) -> (1, 21, 241, 601)
[2025-07-29 14:19:16] [INFO] 处理深度层 1/21 - 0.49402499198913574m (标签: 0)
[2025-07-29 14:19:17] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:18] [SUCCESS] 已生成文件: depth_current_0.json (包含 134068 个数据点)
[2025-07-29 14:19:18] [INFO] 处理深度层 2/21 - 1.5413750410079956m (标签: 2)
[2025-07-29 14:19:19] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:19] [SUCCESS] 已生成文件: depth_current_2.json (包含 134068 个数据点)
[2025-07-29 14:19:19] [INFO] 处理深度层 3/21 - 2.6456689834594727m (标签: 3)
[2025-07-29 14:19:21] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:21] [SUCCESS] 已生成文件: depth_current_3.json (包含 134068 个数据点)
[2025-07-29 14:19:21] [INFO] 处理深度层 4/21 - 3.8194949626922607m (标签: 4)
[2025-07-29 14:19:22] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:22] [SUCCESS] 已生成文件: depth_current_4.json (包含 134068 个数据点)
[2025-07-29 14:19:22] [INFO] 处理深度层 5/21 - 5.078224182128906m (标签: 5)
[2025-07-29 14:19:24] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:24] [SUCCESS] 已生成文件: depth_current_5.json (包含 134068 个数据点)
[2025-07-29 14:19:24] [INFO] 处理深度层 6/21 - 6.440614223480225m (标签: 6)
[2025-07-29 14:19:25] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:26] [SUCCESS] 已生成文件: depth_current_6.json (包含 134068 个数据点)
[2025-07-29 14:19:26] [INFO] 处理深度层 7/21 - 7.92956018447876m (标签: 8)
[2025-07-29 14:19:27] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:27] [SUCCESS] 已生成文件: depth_current_8.json (包含 133792 个数据点)
[2025-07-29 14:19:27] [INFO] 处理深度层 8/21 - 9.572997093200684m (标签: 10)
[2025-07-29 14:19:29] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:29] [SUCCESS] 已生成文件: depth_current_10.json (包含 133739 个数据点)
[2025-07-29 14:19:29] [INFO] 处理深度层 9/21 - 11.404999732971191m (标签: 11)
[2025-07-29 14:19:30] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:30] [SUCCESS] 已生成文件: depth_current_11.json (包含 133676 个数据点)
[2025-07-29 14:19:30] [INFO] 处理深度层 10/21 - 13.467140197753906m (标签: 14)
[2025-07-29 14:19:32] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:32] [SUCCESS] 已生成文件: depth_current_14.json (包含 133614 个数据点)
[2025-07-29 14:19:32] [INFO] 处理深度层 11/21 - 15.810070037841797m (标签: 16)
[2025-07-29 14:19:33] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:34] [SUCCESS] 已生成文件: depth_current_16.json (包含 133556 个数据点)
[2025-07-29 14:19:34] [INFO] 处理深度层 12/21 - 18.495559692382812m (标签: 19)
[2025-07-29 14:19:35] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:35] [SUCCESS] 已生成文件: depth_current_19.json (包含 133455 个数据点)
[2025-07-29 14:19:35] [INFO] 处理深度层 13/21 - 21.598819732666016m (标签: 22)
[2025-07-29 14:19:36] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:37] [SUCCESS] 已生成文件: depth_current_22.json (包含 133344 个数据点)
[2025-07-29 14:19:37] [INFO] 处理深度层 14/21 - 25.211410522460938m (标签: 25)
[2025-07-29 14:19:38] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:38] [SUCCESS] 已生成文件: depth_current_25.json (包含 133232 个数据点)
[2025-07-29 14:19:38] [INFO] 处理深度层 15/21 - 29.444730758666992m (标签: 29)
[2025-07-29 14:19:41] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:41] [SUCCESS] 已生成文件: depth_current_29.json (包含 133134 个数据点)
[2025-07-29 14:19:41] [INFO] 处理深度层 16/21 - 34.43415069580078m (标签: 34)
[2025-07-29 14:19:42] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:42] [SUCCESS] 已生成文件: depth_current_34.json (包含 133022 个数据点)
[2025-07-29 14:19:42] [INFO] 处理深度层 17/21 - 40.344051361083984m (标签: 40)
[2025-07-29 14:19:44] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:44] [SUCCESS] 已生成文件: depth_current_40.json (包含 132908 个数据点)
[2025-07-29 14:19:44] [INFO] 处理深度层 18/21 - 47.37369155883789m (标签: 47)
[2025-07-29 14:19:46] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:46] [SUCCESS] 已生成文件: depth_current_47.json (包含 132768 个数据点)
[2025-07-29 14:19:46] [INFO] 处理深度层 19/21 - 55.76428985595703m (标签: 56)
[2025-07-29 14:19:47] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:48] [SUCCESS] 已生成文件: depth_current_56.json (包含 132612 个数据点)
[2025-07-29 14:19:48] [INFO] 处理深度层 20/21 - 65.80726623535156m (标签: 66)
[2025-07-29 14:19:49] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:49] [SUCCESS] 已生成文件: depth_current_66.json (包含 132434 个数据点)
[2025-07-29 14:19:49] [INFO] 处理深度层 21/21 - 77.85385131835938m (标签: 78)
[2025-07-29 14:19:51] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:51] [SUCCESS] 已生成文件: depth_current_78.json (包含 132242 个数据点)
[2025-07-29 14:19:51] [SUCCESS] 合并海流数据处理完成，生成 32 个文件
[2025-07-29 14:19:51] [SUCCESS] 合并海流数据处理完成，生成 32 个文件
[2025-07-29 14:19:51] 
=== 步骤 5: 处理海表流场数据 ===
[2025-07-29 14:19:51] [INFO] 处理配置文件: surface_current.yaml (海表流场数据)
[2025-07-29 14:19:51] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\config\surface_current.yaml
[2025-07-29 14:19:51] [INFO] 开始处理海表流场数据...
[2025-07-29 14:19:51] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\surface_current\surface_current.nc
[2025-07-29 14:19:51] [INFO] 海表流场变量: U分量=utotal, V分量=vtotal
[2025-07-29 14:19:51] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\data\2025_07_28\surface_current\surface_current.nc
[2025-07-29 14:19:51] [SUCCESS] NetCDF文件数据读取成功
[2025-07-29 14:19:51] [INFO] 海表流场数据形状 - utotal: (1, 1, 721, 1801), vtotal: (1, 1, 721, 1801)
[2025-07-29 14:19:51] [INFO] 开始降采样海表流场数据...
[2025-07-29 14:19:51] [INFO] 降采样: (1, 1, 721, 1801) -> (1, 1, 241, 601)
[2025-07-29 14:19:51] [INFO] 降采样: (1, 1, 721, 1801) -> (1, 1, 241, 601)
[2025-07-29 14:19:52] [INFO] 使用默认输出路径
[2025-07-29 14:19:52] [INFO] 海表流场输出目录: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\output\2025_07_28
[2025-07-29 14:19:52] [INFO] 海表流场输出文件: surface_current.json
[2025-07-29 14:19:52] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-29 14:19:52] [SUCCESS] 已生成文件: surface_current.json (包含 134068 个数据点)
[2025-07-29 14:19:52] [SUCCESS] 海表流场数据处理完成，生成 1 个文件
[2025-07-29 14:19:52] [SUCCESS] 海表流场数据处理完成，生成 1 个文件
[2025-07-29 14:19:52] 
=== 步骤 6: 处理涌浪数据 ===
[2025-07-29 14:19:52] [INFO] 处理配置文件: swell_waves.yaml (涌浪数据)
[2025-07-29 14:19:52] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\config\swell_waves.yaml
[2025-07-29 14:19:52] [INFO] 开始处理涌浪数据...
[2025-07-29 14:19:52] [INFO] 方向文件: data\2025_07_28\direction_of_swell_waves\direction_of_swell_waves.nc
[2025-07-29 14:19:52] [INFO] 周期文件: data\2025_07_28\mean_period_of_total_swell\mean_period_of_total_swell.nc
[2025-07-29 14:19:52] [INFO] 正在读取文件: data\2025_07_28\direction_of_swell_waves\direction_of_swell_waves.nc
[2025-07-29 14:19:53] [SUCCESS] NetCDF文件数据读取成功
[2025-07-29 14:19:53] [INFO] 正在读取文件: data\2025_07_28\mean_period_of_total_swell\mean_period_of_total_swell.nc
[2025-07-29 14:19:53] [ERROR] 读取文件失败: [Errno 2] No such file or directory: 'data\\2025_07_28\\mean_period_of_total_swell\\mean_period_of_total_swell.nc'
[2025-07-29 14:19:53] [ERROR] 处理配置文件 swell_waves.yaml 失败: 无法读取NC文件数据: data\2025_07_28\mean_period_of_total_swell\mean_period_of_total_swell.nc - [Errno 2] No such file or directory: 'data\\2025_07_28\\mean_period_of_total_swell\\mean_period_of_total_swell.nc'
[2025-07-29 14:19:53] 
=== 处理完成 ===
[2025-07-29 14:19:53] [INFO] 总共生成 47 个JSON文件
[2025-07-29 14:19:53] [INFO] 完整日志已保存到: D:\GIT\fisherycube-data-process\vector_generator_v0.6\dist\log\nc_to_geojson_202507291416.log
