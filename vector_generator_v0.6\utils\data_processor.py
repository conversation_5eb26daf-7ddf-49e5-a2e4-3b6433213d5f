#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
data_processor.py
-----------------
NetCDF数据处理模块 - 优化版本
"""

import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Union
from datetime import datetime

# 移除对run_nc_reader的依赖，直接使用utils.module中的函数
from utils.module import calculate_wind_vectors, convert_unix_to_iso, create_point_features
from utils.config_manager import (
    get_nc_file_path, get_variable_names, get_forecast_hours, get_output_config,
    get_current_output_config, get_surface_current_config, get_surface_variable_names,
    get_nc_file_path_from_source
)
from utils.geojson_utils import round_coordinates, create_geojson_output, safe_json_dumps, format_geojson_output


def _read_nc_data(file_path: Path, variable_names: List[str], logger) -> Dict:
    """读取NetCDF数据的通用函数"""
    logger.log_print(f"[INFO] 正在读取文件: {file_path}")
    
    # 导入必要的模块进行直接文件读取
    from utils.module import open_nc_file, close_nc_file, get_variable_data, add_coordinate_variables
    
    try:
        # 直接打开指定的文件
        ds = open_nc_file(file_path, "netcdf4")
        
        # 添加坐标变量到变量名列表
        var_names = add_coordinate_variables(variable_names, ds)
        
        # 获取所有变量的数据
        variables = {}
        for var_name in var_names:
            data = get_variable_data(ds, var_name)
            if data is not None:
                variables[var_name] = data
        
        # 关闭文件
        close_nc_file(ds, "netcdf4")
        
        if not variables:
            raise ValueError(f"无法从文件中读取任何变量数据: {file_path}")
        
        logger.log_print("[SUCCESS] NetCDF文件数据读取成功")
        return variables
        
    except Exception as e:
        logger.log_print(f"[ERROR] 读取文件失败: {e}")
        raise ValueError(f"无法读取NC文件数据: {file_path} - {e}")


def _create_grid_and_extract_data(variables: Dict, config: Dict, logger) -> Tuple:
    """创建网格并提取数据的通用函数"""
    lons = variables["longitude"]
    lats = variables["latitude"]
    
    # 获取u和v分量变量名
    data_config = config.get('data', {})
    variables_config = data_config.get('variables', {})
    u_var = variables_config.get('u_component', 'u10')
    v_var = variables_config.get('v_component', 'v10')
    
    u_data = variables[u_var]
    v_data = variables[v_var]
    times = variables.get("valid_time")
    
    logger.log_print(f"[INFO] 数据维度 - 经度: {len(lons)}, 纬度: {len(lats)}")
    if times is not None:
        logger.log_print(f"[INFO] 时间步: {len(times)}")
    
    # 创建经纬度网格
    lon_grid, lat_grid = np.meshgrid(lons, lats)
    
    return lons, lats, u_data, v_data, times, lon_grid, lat_grid


def _downsample_to_quarter_degree(lons, lats, data, logger):
    """将1/12°数据降采样到1/4°分辨率"""
    downsample_factor = 3
    downsampled_lons = lons[::downsample_factor]
    downsampled_lats = lats[::downsample_factor]
    
    # 根据数据维度进行降采样
    if len(data.shape) == 2:
        downsampled_data = data[::downsample_factor, ::downsample_factor]
    elif len(data.shape) == 3:
        downsampled_data = data[:, ::downsample_factor, ::downsample_factor]
    elif len(data.shape) == 4:
        downsampled_data = data[:, :, ::downsample_factor, ::downsample_factor]
    else:
        raise ValueError(f"不支持的数据维度: {len(data.shape)}")
    
    logger.log_print(f"[INFO] 降采样: {data.shape} -> {downsampled_data.shape}")
    return downsampled_lons, downsampled_lats, downsampled_data


def _save_geojson_file(features: List[Dict], file_path: Path, logger) -> str:
    """保存GeoJSON文件的通用函数"""
    # 规范化坐标精度
    features = round_coordinates(features)
    
    # 保存到JSON文件（使用压缩格式）
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(format_geojson_output(features, logger, compress=True))
    
    logger.log_print(f"[SUCCESS] 已生成文件: {file_path.name} (包含 {len(features)} 个数据点)")
    return str(file_path)


def _process_time_steps(u_data, v_data, times, lon_grid, lat_grid, 
                       output_dir, forecast_hours, output_pattern, logger) -> List[str]:
    """处理时间步数据的通用函数"""
    generated_files = []
    
    for t in range(len(times)):
        if t >= len(forecast_hours):
            logger.log_print(f"[WARNING] 时间步 {t} 超出预报时间列表范围，跳过")
            break
            
        logger.log_print(f"[INFO] 处理时间步 {t+1}/{len(times)}")
        
        # 获取当前时间步的u和v分量
        u = u_data[t]
        v = v_data[t]
        
        # 计算风速和风向
        speeds, directions = calculate_wind_vectors(u, v)
        
        # 获取Unix时间戳
        time_timestamp = times[t]
        time_iso = convert_unix_to_iso(times[t])
        logger.log_print(f"[INFO] 时间: {time_iso}")
        
        # 创建当前时间步的特征
        features = create_point_features(lon_grid, lat_grid, speeds, directions, time_timestamp)
        
        # 获取预报小时数并创建文件名
        forecast_hour = forecast_hours[t]
        filename = output_pattern.replace('{time_label}', str(forecast_hour))
        file_path = output_dir / filename
        
        # 保存文件
        generated_files.append(_save_geojson_file(features, file_path, logger))
    
    return generated_files


def _process_depth_layers(uo, vo, depths, lons, lats,
                         output_dir, output_pattern, depth_labels, logger) -> List[str]:
    """处理深度层数据的通用函数"""
    generated_files = []
    
    logger.log_print(f"[INFO] 原始海流数据维度 - uo: {uo.shape}, vo: {vo.shape}")
    
    # 对海流数据进行降采样
    logger.log_print("[INFO] 开始降采样海流数据...")
    downsampled_lons, downsampled_lats, downsampled_uo = _downsample_to_quarter_degree(lons, lats, uo, logger)
    _, _, downsampled_vo = _downsample_to_quarter_degree(lons, lats, vo, logger)
    
    # 创建降采样后的经纬度网格
    lon_grid, lat_grid = np.meshgrid(downsampled_lons, downsampled_lats)
    
    # 处理每个深度层
    for d in range(len(depths)):
        if d >= len(depth_labels):
            logger.log_print(f"[WARNING] 深度层 {d} 超出深度标签列表范围，跳过")
            break
            
        depth_value = depths[d]
        depth_label = depth_labels[d]
        logger.log_print(f"[INFO] 处理深度层 {d+1}/{len(depths)} - {depth_value}m (标签: {depth_label})")
        
        # 根据数据维度提取当前深度层的数据
        if len(downsampled_uo.shape) == 4:
            u = downsampled_uo[0, d, :, :]
            v = downsampled_vo[0, d, :, :]
        elif len(downsampled_uo.shape) == 3:
            u = downsampled_uo[d, :, :]
            v = downsampled_vo[d, :, :]
        else:
            logger.log_print(f"[ERROR] 不支持的数据维度: {downsampled_uo.shape}")
            continue
        
        # 确保数组形状匹配
        if u.shape != lon_grid.shape or v.shape != lat_grid.shape:
            logger.log_print(f"[WARNING] 数组形状不匹配，跳过深度层 {depth_label}")
            continue
        
        # 计算海流速度和方向
        speeds, directions = calculate_wind_vectors(u, v)
        
        # 创建Unix时间戳
        time_timestamp = datetime.utcnow().timestamp()
        
        # 创建当前深度层的特征
        features = create_point_features(lon_grid, lat_grid, speeds, directions, 
                                       time_timestamp, depth_value=depth_value)
        
        # 创建文件名并保存
        filename = output_pattern.format(depth_label=depth_label)
        file_path = output_dir / filename
        generated_files.append(_save_geojson_file(features, file_path, logger))
    
    return generated_files


def process_combined_wind_data(config: Dict, logger) -> List[str]:
    """处理合并的风分量数据（同时包含u10和v10）"""
    logger.log_print("[INFO] 开始处理合并的风分量数据")
    
    # 获取文件路径和配置
    nc_file = get_nc_file_path(config, logger)
    variable_names = get_variable_names(config, logger)
    
    # 读取数据
    variables = _read_nc_data(nc_file, variable_names, logger)
    
    # 提取数据并创建网格
    lons, lats, u_data, v_data, times, lon_grid, lat_grid = _create_grid_and_extract_data(variables, config, logger)
    
    # 获取输出配置
    output_dir, date_folder, output_prefix = get_output_config(config, logger)
    forecast_hours = get_forecast_hours(config, logger)
    
    # 获取输出模式
    output_config = config.get('output', {})
    output_pattern = output_config.get('output_pattern', f"{output_prefix}_{{time_label}}.json")
    
    return _process_time_steps(u_data, v_data, times, lon_grid, lat_grid, 
                              output_dir, forecast_hours, output_pattern, logger)


def process_merged_wind_data(config: Dict, logger) -> List[str]:
    """处理合并的风分量数据配置（读取分离的U和V文件）"""
    logger.log_print("[INFO] 开始处理合并的风分量数据")
    
    # 获取数据源配置
    data_config = config.get('data', {})
    data_sources = data_config.get('data_sources', {})
    date_folder = data_config.get('date_folder', '2025_06_30')
    time_configs = data_config.get('time_configs', {})
    
    # 验证U和V分量数据源存在
    if 'u_component' not in data_sources or 'v_component' not in data_sources:
        raise ValueError("缺少必需的U或V分量配置")
    
    u_source = data_sources['u_component']
    v_source = data_sources['v_component']
    
    # 构建文件路径
    u_file_path = get_nc_file_path_from_source(u_source, date_folder, logger)
    v_file_path = get_nc_file_path_from_source(v_source, date_folder, logger)
    
    # 获取变量名
    u_var = u_source.get('variable_name', 'u10')
    v_var = v_source.get('variable_name', 'v10')
    
    # 读取U和V分量数据
    u_variables = _read_nc_data(u_file_path, ["longitude", "latitude", u_var, "valid_time"], logger)
    v_variables = _read_nc_data(v_file_path, ["longitude", "latitude", v_var, "valid_time"], logger)
    
    # 提取数据
    lons = u_variables["longitude"]
    lats = u_variables["latitude"]
    u_data = u_variables[u_var]
    v_data = v_variables[v_var]
    times = u_variables["valid_time"]
    
    logger.log_print(f"[INFO] 数据维度 - 经度: {len(lons)}, 纬度: {len(lats)}, 时间步: {len(times)}")
    
    # 创建经纬度网格
    lon_grid, lat_grid = np.meshgrid(lons, lats)
    
    # 获取输出配置
    output_config = config.get('output', {})
    base_path_template = output_config.get('base_path', f"./output/2025_07_22/")
    # 替换模板变量
    base_path_str = base_path_template.replace('2025_07_22', date_folder)
    base_path = Path(base_path_str)
    base_path.mkdir(parents=True, exist_ok=True)
    
    # 从配置中获取输出模式和预报时间列表
    output_pattern = output_config.get('output_pattern', 'wind_10m_gfs_{time_label}.json')
    forecast_hours = time_configs.get('time_labels', [24, 48, 72, 96, 120, 144, 168])
    
    return _process_time_steps(u_data, v_data, times, lon_grid, lat_grid,
                              base_path, forecast_hours, output_pattern, logger)


def process_surface_current_data(config: Dict, logger) -> List[str]:
    """处理海表流场数据"""
    logger.log_print("[INFO] 开始处理海表流场数据...")
    
    # 获取文件路径和变量名
    file_path = get_nc_file_path(config, logger)
    u_var, v_var = get_surface_variable_names(config, logger)
    
    # 读取数据
    variable_names = ["longitude", "latitude", u_var, v_var]
    variables_data = _read_nc_data(file_path, variable_names, logger)
    
    # 提取数据
    lons = variables_data["longitude"]
    lats = variables_data["latitude"] 
    utotal = variables_data[u_var]
    vtotal = variables_data[v_var]
    
    logger.log_print(f"[INFO] 海表流场数据形状 - utotal: {utotal.shape}, vtotal: {vtotal.shape}")
    
    # 对海表流场数据进行降采样
    logger.log_print("[INFO] 开始降采样海表流场数据...")
    downsampled_lons, downsampled_lats, downsampled_utotal = _downsample_to_quarter_degree(lons, lats, utotal, logger)
    _, _, downsampled_vtotal = _downsample_to_quarter_degree(lons, lats, vtotal, logger)
    
    # 创建降采样后的经纬度网格
    lon_grid, lat_grid = np.meshgrid(downsampled_lons, downsampled_lats)
    
    # 根据数据维度提取海表层数据
    if len(downsampled_utotal.shape) == 4:
        u = downsampled_utotal[0, 0, :, :]
        v = downsampled_vtotal[0, 0, :, :]
    elif len(downsampled_utotal.shape) == 3:
        u = downsampled_utotal[0, :, :]
        v = downsampled_vtotal[0, :, :]
    elif len(downsampled_utotal.shape) == 2:
        u = downsampled_utotal
        v = downsampled_vtotal
    else:
        raise ValueError(f"不支持的数据维度: {downsampled_utotal.shape}")
    
    # 确保数组形状匹配
    if u.shape != lon_grid.shape or v.shape != lat_grid.shape:
        raise ValueError(f"数组形状不匹配: u={u.shape}, v={v.shape}, 网格={lon_grid.shape}")
    
    # 计算海表流速和方向
    speeds, directions = calculate_wind_vectors(u, v)
    
    # 创建Unix时间戳
    time_timestamp = datetime.utcnow().timestamp()
    
    # 创建海表流场特征（深度设为0表示海表面）
    features = create_point_features(lon_grid, lat_grid, speeds, directions, 
                                   time_timestamp, depth_value=0.0)
    
    # 获取输出配置并保存
    output_dir, output_filename = get_surface_current_config(config, logger)
    file_path = output_dir / output_filename
    
    generated_files = [_save_geojson_file(features, file_path, logger)]
    logger.log_print(f"[SUCCESS] 海表流场数据处理完成，生成 {len(generated_files)} 个文件")
    
    return generated_files


def process_merged_current_data(config: Dict, logger) -> List[str]:
    """处理合并的海流深度数据配置"""
    logger.log_print("[INFO] 开始处理合并的海流深度数据")
    
    # 获取数据源配置
    data_config = config.get('data', {})
    data_sources = data_config.get('data_sources', {})
    date_folder = data_config.get('date_folder', '2025_06_30')
    depth_configs = data_config.get('depth_configs', {})
    
    # 验证必需的数据源
    required_sources = ['eastward_deep', 'northward_deep', 'eastward_shallow', 'northward_shallow']
    for source in required_sources:
        if source not in data_sources:
            raise ValueError(f"缺少必需的数据源配置: {source}")
    
    # 读取各个数据源
    source_data = {}
    for source_name, source_config in data_sources.items():
        logger.log_print(f"[INFO] 正在读取 {source_name} 数据")
        
        try:
            data_path = get_nc_file_path_from_source(source_config, date_folder, logger)
            variable_name = source_config.get('variable_name', 'uo' if 'eastward' in source_name else 'vo')
            variables = _read_nc_data(data_path, ["longitude", "latitude", variable_name, "depth"], logger)
            source_data[source_name] = {'data': variables, 'config': source_config}
        except FileNotFoundError as e:
            logger.log_print(f"[WARNING] {e}")
            continue
    
    if len(source_data) < 4:
        raise ValueError(f"只成功读取了 {len(source_data)}/4 个数据源，处理中止")
    
    # 获取输出配置
    output_config = config.get('output', {})
    base_path_template = output_config.get('base_path', f'./output/2025_07_22/')
    # 替换模板变量
    base_path_str = base_path_template.replace('2025_07_22', date_folder)
    base_path = Path(base_path_str)
    base_path.mkdir(parents=True, exist_ok=True)
    
    generated_files = []
    
    # 处理深层和浅层数据
    for layer_type, pattern_key in [('deep', 'deep_pattern'), ('shallow', 'shallow_pattern')]:
        eastward_key = f'eastward_{layer_type}'
        northward_key = f'northward_{layer_type}'
        
        if eastward_key in source_data and northward_key in source_data:
            logger.log_print(f"[INFO] 处理{layer_type}层海流数据...")
            files = _process_current_pair(
                source_data[eastward_key], 
                source_data[northward_key],
                base_path, 
                output_config.get(pattern_key, f'depth_current_{layer_type}_{{depth_label}}.json'),
                logger,
                depth_configs
            )
            generated_files.extend(files)
    
    logger.log_print(f"[SUCCESS] 合并海流数据处理完成，生成 {len(generated_files)} 个文件")
    return generated_files


def _process_current_pair(u_source: Dict, v_source: Dict, output_dir: Path, 
                         output_pattern: str, logger, depth_configs: Dict = None) -> List[str]:
    """处理一对海流数据（U分量和V分量）"""
    # 提取数据
    u_data = u_source['data']
    v_data = v_source['data']
    u_config = u_source['config']
    
    lons = u_data["longitude"]
    lats = u_data["latitude"]
    u_variable_name = u_config.get('variable_name', 'uo')
    v_variable_name = v_source['config'].get('variable_name', 'vo')
    uo = u_data[u_variable_name]
    vo = v_data[v_variable_name]
    depths = u_data["depth"]
    
    logger.log_print(f"[INFO] 数据维度 - 经度: {len(lons)}, 纬度: {len(lats)}, 深度: {len(depths)}")
    
    # 获取深度标签
    depth_config_name = u_config.get('depth_config', 'deep')
    if depth_configs and depth_config_name in depth_configs:
        depth_labels = depth_configs[depth_config_name].get('depth_labels', [])
    else:
        # 默认深度标签
        depth_labels = [78, 92, 110, 131, 156, 186, 222, 266, 318, 380, 454] if depth_config_name == 'deep' else [0, 2, 3, 4, 5, 6, 8, 10, 11, 14, 16, 19, 22, 25, 29, 34, 40, 47, 56, 66, 78]
    
    return _process_depth_layers(uo, vo, depths, lons, lats, output_dir, output_pattern, depth_labels, logger) 


def process_swell_waves_data(config: Dict, logger) -> List[str]:
    """处理涌浪数据（合并方向和周期）"""
    logger.log_print("[INFO] 开始处理涌浪数据...")
    
    # 获取配置信息
    data_config = config.get('data', {})
    date_folder = data_config.get('date_folder', '2025_07_07')
    input_file_config = data_config.get('input_file', {})
    variables_config = data_config.get('variables', {})
    
    # 获取方向和周期配置
    direction_config = input_file_config.get('direction', {})
    period_config = input_file_config.get('period', {})
    
    if not direction_config or not period_config:
        raise ValueError("涌浪数据配置中缺少direction或period配置")
    
    # 构建文件路径
    direction_file_template = direction_config.get('input_file', '')
    period_file_template = period_config.get('input_file', '')
    
    direction_file = Path(direction_file_template.replace('2025_07_22', date_folder))
    period_file = Path(period_file_template.replace('2025_07_22', date_folder))
    
    logger.log_print(f"[INFO] 方向文件: {direction_file}")
    logger.log_print(f"[INFO] 周期文件: {period_file}")
    
    # 获取变量名
    direction_var = direction_config.get('variable', 'swdir')
    period_var = period_config.get('variable', 'mpts')
    
    # 读取方向数据
    direction_variables = _read_nc_data(direction_file, 
                                      ["longitude", "latitude", direction_var, "valid_time"], 
                                      logger)
    
    # 读取周期数据  
    period_variables = _read_nc_data(period_file,
                                   ["longitude", "latitude", period_var, "valid_time"],
                                   logger)
    
    # 提取数据
    lons = direction_variables["longitude"]
    lats = direction_variables["latitude"]
    swdir_data = direction_variables[direction_var]
    mpts_data = period_variables[period_var]
    times = direction_variables.get("valid_time")
    
    logger.log_print(f"[INFO] 数据维度 - 经度: {len(lons)}, 纬度: {len(lats)}")
    if times is not None:
        logger.log_print(f"[INFO] 时间步: {len(times)}")
    
    # 创建经纬度网格
    lon_grid, lat_grid = np.meshgrid(lons, lats)
    
    # 获取输出配置
    output_config = config.get('output', {})
    base_path_template = output_config.get('base_path', f'./output/2025_07_22/')
    # 替换模板变量
    base_path_str = base_path_template.replace('2025_07_22', date_folder)
    output_dir = Path(base_path_str)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取输出模式和时间标签
    output_pattern = output_config.get('output_pattern', 'swell_waves_{time_label}.json')
    separate_times = output_config.get('separate_times', True)
    
    # 处理时间标签
    multilayer_config = data_config.get('multilayer', {})
    time_labels = multilayer_config.get('time_labels', 7)  # 默认7个时间步
    
    # 创建时间标签列表
    if isinstance(time_labels, int):
        forecast_hours = [24 * (i + 1) for i in range(time_labels)]  # 24h, 48h, 72h...
    else:
        forecast_hours = time_labels
    
    return _process_swell_time_steps(swdir_data, mpts_data, times, lon_grid, lat_grid,
                                   output_dir, forecast_hours, output_pattern, logger, output_config)


def _process_swell_time_steps(swdir_data, mpts_data, times, lon_grid, lat_grid,
                             output_dir, forecast_hours, output_pattern, logger, output_config: Dict = None) -> List[str]:
    """处理涌浪时间步数据"""
    generated_files = []
    
    # 确定时间步数
    if times is not None:
        num_times = min(len(times), len(forecast_hours))
    else:
        num_times = min(swdir_data.shape[0] if len(swdir_data.shape) > 2 else 1, len(forecast_hours))
    
    for t in range(num_times):
        logger.log_print(f"[INFO] 处理时间步 {t+1}/{num_times}")
        
        # 获取当前时间步的方向和周期数据
        if len(swdir_data.shape) == 3:  # 包含时间维度
            swdir = swdir_data[t]
            mpts = mpts_data[t]
        else:  # 不包含时间维度
            swdir = swdir_data
            mpts = mpts_data
        
        # 获取时间戳
        if times is not None:
            time_timestamp = times[t]
            time_iso = convert_unix_to_iso(times[t])
            logger.log_print(f"[INFO] 时间: {time_iso}")
        else:
            # 如果没有时间信息，使用当前时间
            time_timestamp = datetime.now().timestamp()
            time_iso = datetime.now().isoformat() + "Z"
        
        # 创建当前时间步的特征 (speed用mpts，direction用swdir)
        features = create_swell_point_features(lon_grid, lat_grid, mpts, swdir, time_timestamp, output_config)
        
        # 获取预报小时数并创建文件名
        forecast_hour = forecast_hours[t] if t < len(forecast_hours) else forecast_hours[-1]
        filename = output_pattern.replace('{time_label}', str(forecast_hour))
        file_path = output_dir / filename
        
        # 保存文件
        generated_files.append(_save_geojson_file(features, file_path, logger))
        
        # 如果不是多时间步数据，只处理一次
        if len(swdir_data.shape) <= 2:
            break
    
    return generated_files


def create_swell_point_features(lons: np.ndarray, lats: np.ndarray, periods: np.ndarray, 
                               directions: np.ndarray, time: float, output_config: Dict = None) -> List[Dict]:
    """
    为涌浪数据创建GeoJSON Point要素
    
    Args:
        lons: 经度网格
        lats: 纬度网格  
        periods: 周期数据（作为speed）
        directions: 方向数据（作为direction）
        time: Unix时间戳
        output_config: 输出配置（包含经度转换设置）
        
    Returns:
        List[Dict]: GeoJSON Point要素列表
    """
    from utils.module import is_valid_value
    
    features = []
    
    # 展平数组便于迭代
    flat_lons = lons.flatten()
    flat_lats = lats.flatten()
    flat_periods = periods.flatten()
    flat_directions = directions.flatten()
    
    for i in range(len(flat_lons)):
        lon = flat_lons[i] 
        lat = flat_lats[i]
        period = flat_periods[i]  # speed = mpts（周期）
        direction = flat_directions[i]  # dir = swdir（方向）
        
        # 跳过无效值
        if not (is_valid_value(lon) and is_valid_value(lat) and 
                is_valid_value(period) and is_valid_value(direction)):
            continue
        
        # 根据配置决定是否进行经度转换
        if output_config and output_config.get('convert_to_180', False):
            # 转换到[-180, 180]范围
            if lon > 180:
                lon = lon - 360
        # 如果convert_to_180为False，保持原始经度值（0-360范围）
        
        feature = {
            "type": "Feature",
            "geometry": {
                "type": "Point",
                "coordinates": [float(lon), float(lat)]
            },
            "properties": {
                "speed": float(period),      # speed对应mpts（周期）
                "deg": float(direction),     # deg对应swdir（方向）  
                "time": int(time)            # time为Unix时间戳
            }
        }
        
        features.append(feature)
    
    return features 