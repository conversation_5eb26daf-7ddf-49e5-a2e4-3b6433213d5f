#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Created on Tue Jun 26 10:20:42 2025

@author: wanghong<PERSON>

nc_to_geojson.py
----------------
基于YAML配置的NetCDF风场数据转换为GeoJSON格式工具
支持ECMWF和GFS数据源，为每个时间步生成单独的JSON文件

"""

import argparse
from pathlib import Path
from typing import Dict, Callable

# 导入自定义模块
from utils.custom_logger import CustomLogger
from utils.config_manager import update_config_dates, load_yaml_config
from utils.data_processor import (
    process_combined_wind_data, 
    process_merged_current_data, 
    process_surface_current_data, 
    process_merged_wind_data,
    process_swell_waves_data
)
from utils.path_helper import get_config_path, ensure_directories


# 配置文件处理器映射表
CONFIG_PROCESSORS: Dict[str, Dict[str, Callable]] = {
    '10_metre_wind_component': {
        'processor': process_combined_wind_data,
        'description': 'ECMWF风场数据',
        'step_name': '处理ECMWF数据'
    },
    'component_of_wind': {
        'processor': process_merged_wind_data,
        'description': '合并风分量数据',
        'step_name': '处理GFS数据'
    },
    'depth_current': {
        'processor': process_merged_current_data,
        'description': '合并海流数据',
        'step_name': '处理地转流场数据'
    },
    'surface_current': {
        'processor': process_surface_current_data,
        'description': '海表流场数据',
        'step_name': '处理海表流场数据'
    },
    'swell_waves': {
        'processor': process_swell_waves_data,
        'description': '涌浪数据',
        'step_name': '处理涌浪数据'
    }
}

# 配置文件处理顺序（用于--all模式）
CONFIG_PROCESSING_ORDER = [
    '10_metre_wind_component.yaml',
    'component_of_wind.yaml', 
    'depth_current.yaml',
    'surface_current.yaml',
    'swell_waves.yaml'
]


def parse_arguments():
    """
    解析命令行参数
    
    Returns:
        argparse.Namespace: 解析后的参数对象
    """
    parser = argparse.ArgumentParser(
        description='NetCDF风场和海流数据转换为GeoJSON格式工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python nc_to_geojson.py --all                                   # 处理所有配置文件
  python nc_to_geojson.py --config 10_metre_wind_component.yaml   # 处理ECMWF风场数据
  python nc_to_geojson.py --config component_of_wind.yaml         # 处理GFS风分量数据
  python nc_to_geojson.py --config depth_current.yaml             # 处理地转流数据
  python nc_to_geojson.py --config surface_current.yaml           # 处理海表流场数据
  python nc_to_geojson.py --config swell_waves.yaml               # 处理涌浪数据
        """
    )
    
    # 创建互斥参数组
    group = parser.add_mutually_exclusive_group(required=True)
    
    group.add_argument(
        '--all',
        action='store_true',
        help='处理所有配置文件'
    )
    
    group.add_argument(
        '--config',
        type=str,
        help='处理指定的配置文件名（可带或不带.yaml扩展名）'
    )
    
    parser.add_argument(
        '--skip-update',
        action='store_true',
        help='跳过配置文件日期更新步骤'
    )
    
    return parser.parse_args()


def _get_config_processor(config_file: str) -> Dict:
    """
    根据配置文件名获取对应的处理器信息
    
    Args:
        config_file: 配置文件名
        
    Returns:
        Dict: 包含处理器函数和描述信息的字典，如果未找到返回None
    """
    # 移除.yaml扩展名进行匹配
    config_name = config_file.replace('.yaml', '').replace('.yml', '')
    
    for key, processor_info in CONFIG_PROCESSORS.items():
        if key in config_name:
            return processor_info
    
    return None


def _process_config_file(config_file: str, logger, step_name: str = None) -> int:
    """
    处理单个配置文件的通用函数
    
    Args:
        config_file: 配置文件名
        logger: 日志器实例
        step_name: 步骤名称（用于日志显示）
        
    Returns:
        int: 生成的文件数量
    """
    # 规范化文件名
    if not config_file.endswith(('.yaml', '.yml')):
        config_file += '.yaml'
    
    config_path = get_config_path() / config_file
    
    if not config_path.exists():
        logger.log_print(f"[WARNING] 配置文件不存在: {config_path}")
        return 0
    
    # 获取处理器信息
    processor_info = _get_config_processor(config_file)
    if not processor_info:
        logger.log_print(f"[WARNING] 未识别的配置文件类型: {config_file}")
        return 0
    
    processor_func = processor_info['processor']
    description = processor_info['description']
    
    if step_name:
        logger.log_print(f"\n=== {step_name} ===")
    
    logger.log_print(f"[INFO] 处理配置文件: {config_file} ({description})")
    
    try:
        config = load_yaml_config(config_path, logger)
        generated_files = processor_func(config, logger)
        
        file_count = len(generated_files)
        logger.log_print(f"[SUCCESS] {description}处理完成，生成 {file_count} 个文件")
        return file_count
        
    except Exception as e:
        logger.log_print(f"[ERROR] 处理配置文件 {config_file} 失败: {e}")
        return 0


def process_single_config(config_file: str, logger) -> int:
    """
    处理单个配置文件
    
    Args:
        config_file: 配置文件名
        logger: 日志器实例
        
    Returns:
        int: 生成的文件数量
    """
    return _process_config_file(config_file, logger)


def process_all_configs(logger) -> int:
    """
    处理所有配置文件
    
    Args:
        logger: 日志器实例
        
    Returns:
        int: 总共生成的文件数量
    """
    total_generated_files = 0
    step_number = 2  # 从步骤2开始，步骤1是日期更新
    
    try:
        for config_file in CONFIG_PROCESSING_ORDER:
            # 获取处理器信息用于步骤名称
            processor_info = _get_config_processor(config_file)
            step_name = f"步骤 {step_number}: {processor_info['step_name']}" if processor_info else f"步骤 {step_number}: 处理 {config_file}"
            
            file_count = _process_config_file(config_file, logger, step_name)
            total_generated_files += file_count
            step_number += 1
        
        return total_generated_files
        
    except Exception as e:
        logger.log_print(f"[ERROR] 批量处理失败: {e}")
        raise


def main():
    """
    主函数：根据命令行参数处理数据源
    """
    # 解析命令行参数
    args = parse_arguments()
    
    # 确保必要的目录存在
    ensure_directories()
    
    # 初始化日志器
    logger = CustomLogger()
    
    try:
        logger.log_print("=== nc_to_geojson.py 开始执行 ===")
        logger.log_print(f"[INFO] 日志文件: {logger.get_log_file_path()}")
        
        # 显示运行模式
        if args.config:
            logger.log_print(f"[INFO] 运行模式: 单文件处理 ({args.config})")
        elif args.all:
            logger.log_print(f"[INFO] 运行模式: 全部处理")
        else:
            logger.log_print(f"[ERROR] 必须指定 --all 或 --config 参数")
            return
        
        # 1. 更新配置文件日期（可选）
        if not args.skip_update:
            logger.log_print("\n=== 步骤 1: 更新配置文件日期 ===")
            update_config_dates(logger)
        else:
            logger.log_print("\n[INFO] 跳过配置文件日期更新")
        
        total_generated_files = 0
        
        # 根据参数选择处理方式
        if args.config:
            # 处理单个配置文件
            total_generated_files = process_single_config(args.config, logger)
        elif args.all:
            # 处理所有配置文件
            total_generated_files = process_all_configs(logger)
        else:
            logger.log_print(f"[ERROR] 无效的参数组合")
            return
        
        logger.log_print("\n=== 处理完成 ===")
        logger.log_print(f"[INFO] 总共生成 {total_generated_files} 个JSON文件")
        logger.log_print(f"[INFO] 完整日志已保存到: {logger.get_log_file_path()}")
        
    except Exception as e:
        logger.log_print(f"[ERROR] 处理失败: {e}")
        raise


if __name__ == "__main__":
    main()