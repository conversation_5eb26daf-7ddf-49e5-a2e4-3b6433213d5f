#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
geojson_utils.py
----------------
GeoJSON数据处理工具模块
"""

import json
import numpy as np
from typing import Dict, List


class NaNToNullEncoder(json.JSONEncoder):
    """
    自定义JSON编码器，将NaN值转换为null
    """
    def encode(self, obj):
        if isinstance(obj, float) and np.isnan(obj):
            return 'null'
        return super().encode(obj)
    
    def iterencode(self, obj, _one_shot=False):
        """编码对象到JSON字符串，将NaN转换为null"""
        if _one_shot and isinstance(obj, float) and np.isnan(obj):
            yield 'null'
        else:
            for chunk in super().iterencode(obj, _one_shot):
                yield chunk


def safe_json_dumps(obj, **kwargs):
    """
    安全的JSON序列化，自动处理NaN值
    
    Args:
        obj: 要序列化的对象
        **kwargs: 传递给json.dumps的其他参数
        
    Returns:
        str: JSON字符串
    """
    # 递归处理NaN值
    def convert_nan(o):
        if isinstance(o, dict):
            return {k: convert_nan(v) for k, v in o.items()}
        elif isinstance(o, list):
            return [convert_nan(item) for item in o]
        elif isinstance(o, float) and np.isnan(o):
            return None
        else:
            return o
    
    cleaned_obj = convert_nan(obj)
    return json.dumps(cleaned_obj, **kwargs)


def calculate_bbox(features: List[Dict], logger) -> List[float]:
    """
    计算特征集合的边界框
    
    Args:
        features: GeoJSON特征列表
        logger: 日志器实例
        
    Returns:
        List[float]: [west, south, east, north]格式的边界框
    """
    if not features:
        return None
        
    coords = [f['geometry']['coordinates'] for f in features]
    lons = [c[0] for c in coords]
    lats = [c[1] for c in coords]
    
    bbox = [
        round(min(lons), 4),  # west
        round(min(lats), 4),  # south
        round(max(lons), 4),  # east
        round(max(lats), 4)   # north
    ]
    
    logger.log_print(f"[INFO] 计算的边界框: {bbox}")
    return bbox


def round_coordinates(features: List[Dict]) -> List[Dict]:
    """
    规范化坐标和数值精度
    
    Args:
        features: GeoJSON特征列表
        
    Returns:
        List[Dict]: 处理后的特征列表
    """
    for feature in features:
        coords = feature['geometry']['coordinates']
        feature['geometry']['coordinates'] = [
            round(coords[0], 4),  # 经度保留4位小数
            round(coords[1], 4)   # 纬度保留4位小数
        ]
        # 规范化属性值精度
        if 'speed' in feature['properties']:
            feature['properties']['speed'] = round(feature['properties']['speed'], 2)
        if 'deg' in feature['properties']:
            feature['properties']['deg'] = round(feature['properties']['deg'], 1)
    return features


def create_geojson_output(features: List[Dict], logger) -> Dict:
    """
    创建完整的GeoJSON对象
    
    Args:
        features: GeoJSON特征列表
        logger: 日志器实例
        
    Returns:
        Dict: 完整的GeoJSON对象
    """
    return {
        "type": "FeatureCollection",
        "bbox": calculate_bbox(features, logger),
        "features": features
    } 


def format_geojson_output(features: List[Dict], logger, compress: bool = False) -> str:
    """
    格式化GeoJSON输出，支持压缩模式和美化模式
    
    Args:
        features: GeoJSON特征列表
        logger: 日志器实例
        compress: 是否压缩输出格式
        
    Returns:
        str: 格式化后的JSON字符串
    """
    geojson_obj = create_geojson_output(features, logger)
    
    if compress:
        return json.dumps(geojson_obj, separators=(',', ':'), ensure_ascii=False)
    else:
        return safe_json_dumps(geojson_obj, indent=2, ensure_ascii=False) 