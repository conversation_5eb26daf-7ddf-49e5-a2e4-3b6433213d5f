[2025-07-30 11:58:13] === nc_to_geojson.py 开始执行 ===
[2025-07-30 11:58:13] [INFO] 日志文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\log\nc_to_geojson_202507301158.log
[2025-07-30 11:58:13] [INFO] 运行模式: 单文件处理 (depth_current.yaml)
[2025-07-30 11:58:13] 
=== 步骤 1: 更新配置文件日期 ===
[2025-07-30 11:58:13] [INFO] 开始更新配置文件日期...
[2025-07-30 11:58:13] [SUCCESS] 配置文件日期更新成功，目标日期: 2025_07_29
[2025-07-30 11:58:13] [INFO] 成功更新 5/5 个配置文件
[2025-07-30 11:58:13] [INFO] 处理配置文件: depth_current.yaml (合并海流数据)
[2025-07-30 11:58:13] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\config\depth_current.yaml
[2025-07-30 11:58:13] [INFO] 开始处理合并的海流深度数据
[2025-07-30 11:58:13] [INFO] 正在读取 eastward_deep 数据
[2025-07-30 11:58:13] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\eastward_current_deep.nc
[2025-07-30 11:58:13] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\eastward_current_deep.nc
[2025-07-30 11:58:13] [SUCCESS] NetCDF文件数据读取成功
[2025-07-30 11:58:13] [INFO] 正在读取 northward_deep 数据
[2025-07-30 11:58:13] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\northward_current_deep.nc
[2025-07-30 11:58:13] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\northward_current_deep.nc
[2025-07-30 11:58:13] [SUCCESS] NetCDF文件数据读取成功
[2025-07-30 11:58:13] [INFO] 正在读取 eastward_shallow 数据
[2025-07-30 11:58:13] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\eastward_current_shallow.nc
[2025-07-30 11:58:13] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\eastward_current_shallow.nc
[2025-07-30 11:58:13] [SUCCESS] NetCDF文件数据读取成功
[2025-07-30 11:58:13] [INFO] 正在读取 northward_shallow 数据
[2025-07-30 11:58:13] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\northward_current_shallow.nc
[2025-07-30 11:58:13] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\northward_current_shallow.nc
[2025-07-30 11:58:14] [SUCCESS] NetCDF文件数据读取成功
[2025-07-30 11:58:14] [INFO] 处理deep层海流数据...
[2025-07-30 11:58:14] [INFO] 数据维度 - 经度: 1801, 纬度: 721, 深度: 11
[2025-07-30 11:58:14] [INFO] 原始海流数据维度 - uo: (1, 11, 721, 1801), vo: (1, 11, 721, 1801)
[2025-07-30 11:58:14] [INFO] 开始降采样海流数据...
[2025-07-30 11:58:14] [INFO] 降采样: (1, 11, 721, 1801) -> (1, 11, 241, 601)
[2025-07-30 11:58:14] [INFO] 降采样: (1, 11, 721, 1801) -> (1, 11, 241, 601)
[2025-07-30 11:58:14] [INFO] 处理深度层 1/11 - 77.85385131835938m (标签: 78)
[2025-07-30 11:58:15] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:58:16] [SUCCESS] 已生成文件: depth_current_78.json (包含 132242 个数据点)
[2025-07-30 11:58:16] [INFO] 处理深度层 2/11 - 92.3260726928711m (标签: 92)
[2025-07-30 11:58:17] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:58:18] [SUCCESS] 已生成文件: depth_current_92.json (包含 132098 个数据点)
[2025-07-30 11:58:18] [INFO] 处理深度层 3/11 - 109.72930145263672m (标签: 110)
[2025-07-30 11:58:19] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:58:20] [SUCCESS] 已生成文件: depth_current_110.json (包含 131940 个数据点)
[2025-07-30 11:58:20] [INFO] 处理深度层 4/11 - 130.66600036621094m (标签: 131)
[2025-07-30 11:58:25] === nc_to_geojson.py 开始执行 ===
[2025-07-30 11:58:25] [INFO] 日志文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\log\nc_to_geojson_202507301158.log
[2025-07-30 11:58:25] [INFO] 运行模式: 单文件处理 (depth_current.yaml)
[2025-07-30 11:58:25] 
=== 步骤 1: 更新配置文件日期 ===
[2025-07-30 11:58:25] [INFO] 开始更新配置文件日期...
[2025-07-30 11:58:25] [SUCCESS] 配置文件日期更新成功，目标日期: 2025_07_29
[2025-07-30 11:58:25] [INFO] 成功更新 5/5 个配置文件
[2025-07-30 11:58:25] [INFO] 处理配置文件: depth_current.yaml (合并海流数据)
[2025-07-30 11:58:25] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\config\depth_current.yaml
[2025-07-30 11:58:25] [INFO] 开始处理合并的海流深度数据
[2025-07-30 11:58:25] [INFO] 正在读取 eastward_deep 数据
[2025-07-30 11:58:25] [WARNING] NetCDF文件不存在: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-28\eastward_current_deep.nc
[2025-07-30 11:58:25] [INFO] 正在读取 northward_deep 数据
[2025-07-30 11:58:25] [WARNING] NetCDF文件不存在: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-28\northward_current_deep.nc
[2025-07-30 11:58:25] [INFO] 正在读取 eastward_shallow 数据
[2025-07-30 11:58:25] [WARNING] NetCDF文件不存在: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-28\eastward_current_shallow.nc
[2025-07-30 11:58:25] [INFO] 正在读取 northward_shallow 数据
[2025-07-30 11:58:25] [WARNING] NetCDF文件不存在: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-28\northward_current_shallow.nc
[2025-07-30 11:58:25] [ERROR] 处理配置文件 depth_current.yaml 失败: 只成功读取了 0/4 个数据源，处理中止
[2025-07-30 11:58:25] 
=== 处理完成 ===
[2025-07-30 11:58:25] [INFO] 总共生成 0 个JSON文件
[2025-07-30 11:58:25] [INFO] 完整日志已保存到: D:\GIT\fisherycube-data-process\vector_generator_v0.6\log\nc_to_geojson_202507301158.log
