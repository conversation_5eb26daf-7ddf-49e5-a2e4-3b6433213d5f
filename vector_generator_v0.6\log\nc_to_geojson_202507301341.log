[2025-07-30 13:41:14] === nc_to_geojson.py 开始执行 ===
[2025-07-30 13:41:14] [INFO] 日志文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\log\nc_to_geojson_202507301341.log
[2025-07-30 13:41:14] [INFO] 运行模式: 单文件处理 (depth_current.yaml)
[2025-07-30 13:41:14] 
[INFO] 跳过配置文件日期更新
[2025-07-30 13:41:14] [INFO] 处理配置文件: depth_current.yaml (合并海流数据)
[2025-07-30 13:41:14] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\config\depth_current.yaml
[2025-07-30 13:41:14] [INFO] 开始处理合并的海流深度数据
[2025-07-30 13:41:14] [INFO] 正在读取 eastward_deep 数据
[2025-07-30 13:41:14] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\eastward_current_deep.nc
[2025-07-30 13:41:14] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\eastward_current_deep.nc
[2025-07-30 13:41:14] [SUCCESS] NetCDF文件数据读取成功
[2025-07-30 13:41:14] [INFO] 正在读取 northward_deep 数据
[2025-07-30 13:41:14] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\northward_current_deep.nc
[2025-07-30 13:41:14] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\northward_current_deep.nc
[2025-07-30 13:41:14] [SUCCESS] NetCDF文件数据读取成功
[2025-07-30 13:41:14] [INFO] 正在读取 eastward_shallow 数据
[2025-07-30 13:41:14] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\eastward_current_shallow.nc
[2025-07-30 13:41:14] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\eastward_current_shallow.nc
[2025-07-30 13:41:15] [SUCCESS] NetCDF文件数据读取成功
[2025-07-30 13:41:15] [INFO] 正在读取 northward_shallow 数据
[2025-07-30 13:41:15] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\northward_current_shallow.nc
[2025-07-30 13:41:15] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\northward_current_shallow.nc
[2025-07-30 13:41:15] [SUCCESS] NetCDF文件数据读取成功
[2025-07-30 13:41:15] [INFO] 处理deep层海流数据...
[2025-07-30 13:41:15] [INFO] 数据维度 - 经度: 1801, 纬度: 721, 深度: 11
[2025-07-30 13:41:15] [INFO] 原始海流数据维度 - uo: (1, 11, 721, 1801), vo: (1, 11, 721, 1801)
[2025-07-30 13:41:15] [INFO] 开始降采样海流数据...
[2025-07-30 13:41:15] [INFO] 降采样: (1, 11, 721, 1801) -> (1, 11, 241, 601)
[2025-07-30 13:41:15] [INFO] 降采样: (1, 11, 721, 1801) -> (1, 11, 241, 601)
[2025-07-30 13:41:15] [INFO] 处理深度层 1/11 - 77.85385131835938m (标签: 78)
[2025-07-30 13:41:17] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 13:41:17] [SUCCESS] 已生成文件: depth_current_78.json (包含 132242 个数据点)
[2025-07-30 13:41:17] [INFO] 处理深度层 2/11 - 92.3260726928711m (标签: 92)
[2025-07-30 13:41:18] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 13:41:19] [SUCCESS] 已生成文件: depth_current_92.json (包含 132098 个数据点)
[2025-07-30 13:41:19] [INFO] 处理深度层 3/11 - 109.72930145263672m (标签: 110)
[2025-07-30 13:41:20] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 13:41:21] [SUCCESS] 已生成文件: depth_current_110.json (包含 131940 个数据点)
[2025-07-30 13:41:21] [INFO] 处理深度层 4/11 - 130.66600036621094m (标签: 131)
[2025-07-30 13:41:22] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 13:41:22] [SUCCESS] 已生成文件: depth_current_131.json (包含 131799 个数据点)
[2025-07-30 13:41:22] [INFO] 处理深度层 5/11 - 155.85069274902344m (标签: 156)
[2025-07-30 13:41:24] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 13:41:24] [SUCCESS] 已生成文件: depth_current_156.json (包含 131646 个数据点)
[2025-07-30 13:41:24] [INFO] 处理深度层 6/11 - 186.12559509277344m (标签: 186)
[2025-07-30 13:41:26] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 13:41:26] [SUCCESS] 已生成文件: depth_current_186.json (包含 131516 个数据点)
[2025-07-30 13:41:26] [INFO] 处理深度层 7/11 - 222.47520446777344m (标签: 222)
[2025-07-30 13:41:28] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 13:41:28] [SUCCESS] 已生成文件: depth_current_222.json (包含 131372 个数据点)
[2025-07-30 13:41:28] [INFO] 处理深度层 8/11 - 266.0403137207031m (标签: 266)
[2025-07-30 13:41:30] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 13:41:30] [SUCCESS] 已生成文件: depth_current_266.json (包含 131273 个数据点)
[2025-07-30 13:41:30] [INFO] 处理深度层 9/11 - 318.1274108886719m (标签: 318)
[2025-07-30 13:41:31] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 13:41:32] [SUCCESS] 已生成文件: depth_current_318.json (包含 131184 个数据点)
[2025-07-30 13:41:32] [INFO] 处理深度层 10/11 - 380.2130126953125m (标签: 380)
