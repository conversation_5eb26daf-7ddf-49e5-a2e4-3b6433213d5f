('D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\build\\build_onefile\\VectorGenerator.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\build\\build_onefile\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\build\\build_onefile\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\build\\build_onefile\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\build\\build_onefile\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\build\\build_onefile\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\build\\build_onefile\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('nc_to_geojson',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\nc_to_geojson.py',
   'PYSOURCE'),
  ('python313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('netCDF4.libs\\zlib-7f23bfc78ec073282c4dcefa16c5e7d5.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\zlib-7f23bfc78ec073282c4dcefa16c5e7d5.dll',
   'BINARY'),
  ('netCDF4.libs\\hdf5_hl-7d9e014e0cb11050006560c382543b4b.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\hdf5_hl-7d9e014e0cb11050006560c382543b4b.dll',
   'BINARY'),
  ('netCDF4.libs\\hdf5-0405898c429aca8f311295e18da7f220.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\hdf5-0405898c429aca8f311295e18da7f220.dll',
   'BINARY'),
  ('netCDF4.libs\\charset-233f44715e5aacaf3a688c2faff5ddf7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\charset-233f44715e5aacaf3a688c2faff5ddf7.dll',
   'BINARY'),
  ('netCDF4.libs\\hdf-a2c6b2bf2154d1ccb038e2378b1f3e85.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\hdf-a2c6b2bf2154d1ccb038e2378b1f3e85.dll',
   'BINARY'),
  ('netCDF4.libs\\libcrypto-3-x64-284a0cdbc44efce574b9633876ffb8b6.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\libcrypto-3-x64-284a0cdbc44efce574b9633876ffb8b6.dll',
   'BINARY'),
  ('netCDF4.libs\\libcurl-2c0350ff925d58e298166926e2fc5515.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\libcurl-2c0350ff925d58e298166926e2fc5515.dll',
   'BINARY'),
  ('netCDF4.libs\\zip-4672775f5aef1bbb8bba773fd1fb567b.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\zip-4672775f5aef1bbb8bba773fd1fb567b.dll',
   'BINARY'),
  ('netCDF4.libs\\jpeg8-6b0e20ead3f1100ffb122e8d7faaa220.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\jpeg8-6b0e20ead3f1100ffb122e8d7faaa220.dll',
   'BINARY'),
  ('netCDF4.libs\\iconv-936c715fc6e91e2a568dc454b5e2ff49.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\iconv-936c715fc6e91e2a568dc454b5e2ff49.dll',
   'BINARY'),
  ('netCDF4.libs\\libbz2-097a16c5f7181c2bd86fb703e052c623.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\libbz2-097a16c5f7181c2bd86fb703e052c623.dll',
   'BINARY'),
  ('netCDF4.libs\\mfhdf-7299049d0634850dce9f5f21eeb4084f.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\mfhdf-7299049d0634850dce9f5f21eeb4084f.dll',
   'BINARY'),
  ('netCDF4.libs\\netcdf-0019d4666d3419591252d98a8ed23bb6.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\netcdf-0019d4666d3419591252d98a8ed23bb6.dll',
   'BINARY'),
  ('netCDF4.libs\\xdr-a886920c05e9c51c6546150293f71c53.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\xdr-a886920c05e9c51c6546150293f71c53.dll',
   'BINARY'),
  ('netCDF4.libs\\libxml2-27ee10ea1a07a176619924d2ef36ef38.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\libxml2-27ee10ea1a07a176619924d2ef36ef38.dll',
   'BINARY'),
  ('netCDF4.libs\\libssh2-642910880af00f7127778e6297bc660b.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\libssh2-642910880af00f7127778e6297bc660b.dll',
   'BINARY'),
  ('netCDF4.libs\\szip-3d56a7c4d7071ea9c488dbf5cb3c691b.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4.libs\\szip-3d56a7c4d7071ea9c488dbf5cb3c691b.dll',
   'BINARY'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('h5py\\h5ac.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5ac.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\_objects.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\_objects.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5fd.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5fd.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5i.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5i.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5p.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5p.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5f.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5f.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5s.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5s.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5d.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5d.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5l.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5l.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5t.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5t.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5r.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5r.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5o.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5o.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5g.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5g.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5a.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5a.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('h5py\\_selector.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\_selector.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5ds.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5ds.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5z.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5z.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\utils.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\defs.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\defs.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\_proxy.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\_proxy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\_npystrings.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\_npystrings.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5pl.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\h5pl.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\_conv.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\_conv.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\_errors.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\_errors.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\yaml\\_yaml.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('cftime\\_cftime.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\cftime\\_cftime.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('netCDF4\\_netCDF4.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\netCDF4\\_netCDF4.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('h5py\\hdf5.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\hdf5.dll',
   'BINARY'),
  ('h5py\\hdf5_hl.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\hdf5_hl.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python3.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes313.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pywin32_system32\\pywintypes313.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\Users\\<USER>\\miniconda3\\ucrtbase.dll', 'BINARY'),
  ('h5py\\zlib.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\h5py\\zlib.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Users\\<USER>\\miniconda3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('config\\10_metre_wind_component.yaml',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\config\\10_metre_wind_component.yaml',
   'DATA'),
  ('config\\component_of_wind.yaml',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\config\\component_of_wind.yaml',
   'DATA'),
  ('config\\depth_current.yaml',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\config\\depth_current.yaml',
   'DATA'),
  ('config\\surface_current.yaml',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\config\\surface_current.yaml',
   'DATA'),
  ('config\\swell_waves.yaml',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\config\\swell_waves.yaml',
   'DATA'),
  ('config\\update\\update.yaml',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\config\\update\\update.yaml',
   'DATA'),
  ('utils\\__init__.py',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__init__.py',
   'DATA'),
  ('utils\\__pycache__\\__init__.cpython-312.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('utils\\__pycache__\\__init__.cpython-313.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\__init__.cpython-39.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\__init__.cpython-39.pyc',
   'DATA'),
  ('utils\\__pycache__\\config_manager.cpython-313.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\config_manager.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\config_manager.cpython-39.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\config_manager.cpython-39.pyc',
   'DATA'),
  ('utils\\__pycache__\\custom_logger.cpython-313.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\custom_logger.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\custom_logger.cpython-39.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\custom_logger.cpython-39.pyc',
   'DATA'),
  ('utils\\__pycache__\\data_processor.cpython-313.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\data_processor.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\data_processor.cpython-39.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\data_processor.cpython-39.pyc',
   'DATA'),
  ('utils\\__pycache__\\geojson_utils.cpython-313.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\geojson_utils.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\geojson_utils.cpython-39.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\geojson_utils.cpython-39.pyc',
   'DATA'),
  ('utils\\__pycache__\\module.cpython-312.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\module.cpython-312.pyc',
   'DATA'),
  ('utils\\__pycache__\\module.cpython-313.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\module.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\module.cpython-39.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\module.cpython-39.pyc',
   'DATA'),
  ('utils\\__pycache__\\path_helper.cpython-313.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\path_helper.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\path_helper.cpython-39.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\path_helper.cpython-39.pyc',
   'DATA'),
  ('utils\\__pycache__\\update_dates.cpython-312.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\update_dates.cpython-312.pyc',
   'DATA'),
  ('utils\\__pycache__\\update_dates.cpython-313.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\update_dates.cpython-313.pyc',
   'DATA'),
  ('utils\\__pycache__\\update_dates.cpython-39.pyc',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\__pycache__\\update_dates.cpython-39.pyc',
   'DATA'),
  ('utils\\config_manager.py',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\config_manager.py',
   'DATA'),
  ('utils\\custom_logger.py',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\custom_logger.py',
   'DATA'),
  ('utils\\data_processor.py',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\data_processor.py',
   'DATA'),
  ('utils\\geojson_utils.py',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\geojson_utils.py',
   'DATA'),
  ('utils\\module.py',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\module.py',
   'DATA'),
  ('utils\\path_helper.py',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\path_helper.py',
   'DATA'),
  ('utils\\update_dates.py',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\utils\\update_dates.py',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('base_library.zip',
   'D:\\GIT\\fisherycube-data-process\\vector_generator_v0.6\\build\\build_onefile\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 ['vcruntime140.dll', 'python3*.dll'],
 None,
 None,
 None)
