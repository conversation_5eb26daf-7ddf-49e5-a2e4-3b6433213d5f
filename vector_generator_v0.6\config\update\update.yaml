# config/update/update.yaml
# 日期更新主配置文件

# 日期更新模式，可选: 'today', 'days_ago', 'specific'
date_mode: 'days_ago'      # 'today' 表示今天, 'days_ago' 表示N天前, 'specific' 表示指定日期

# days_ago模式参数
days_ago: 1                # 1=昨天, 3=3天前, -1=明天

# specific模式参数
specific_date: '2025_06_25'  # 固定日期，格式: YYYY_MM_DD

# 配置文件目录
config_dir: './config'     # 主配置文件目录

# 是否显示详细处理过程
verbose: true             # true=详细输出, false=静默模式

# 高级设置
settings:
  auto_create_output_dir: true      # 自动创建输出目录
  backup_config_files: false        # 是否备份配置文件
  backup_dir: './backup'            # 备份目录
  date_format: '%Y_%m_%d'           # 日期格式
  config_extensions: ['*.yaml', '*.yml']  # 支持的配置文件扩展名