#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
custom_logger.py
----------------
自定义日志处理模块，专用于nc_to_geojson工具
"""

from datetime import datetime
from pathlib import Path
from utils.path_helper import get_log_path


class CustomLogger:
    """自定义日志处理器，专用于nc_to_geojson"""
    
    def __init__(self, log_dir: str = None):
        """
        初始化日志器
        
        Args:
            log_dir: 日志目录，如果为None则使用默认路径
        """
        if log_dir is None:
            self.log_dir = get_log_path()
        else:
            self.log_dir = Path(log_dir)
        
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建日志文件名（精确到年月日时分）
        current_time = datetime.now()
        log_filename = f"nc_to_geojson_{current_time.strftime('%Y%m%d%H%M')}.log"
        self.log_file_path = self.log_dir / log_filename
    
    def log_print(self, message: str):
        """同时打印到控制台和记录到日志文件"""
        print(message)
        
        # 移除特殊字符，只记录纯文本到日志
        clean_message = (message
                        .replace("[SUCCESS]", "[SUCCESS]")
                        .replace("[ERROR]", "[ERROR]")
                        .replace("[WARNING]", "[WARNING]")
                        .replace("[INFO]", "[INFO]")
                        .replace("[TIME]", "[TIME]")
                        .replace("[FILE]", "[FILE]")
                        .replace("[LOG]", "[LOG]"))
        
        # 写入文件日志
        with open(self.log_file_path, 'a', encoding='utf-8') as f:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"[{timestamp}] {clean_message}\n")
    
    def get_log_file_path(self) -> str:
        """获取日志文件路径"""
        return str(self.log_file_path) 