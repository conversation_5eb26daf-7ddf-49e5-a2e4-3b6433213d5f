[2025-07-30 13:35:22] === nc_to_geojson.py 开始执行 ===
[2025-07-30 13:35:22] [INFO] 日志文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\log\nc_to_geojson_202507301335.log
[2025-07-30 13:35:22] [INFO] 运行模式: 单文件处理 (depth_current.yaml)
[2025-07-30 13:35:22] 
=== 步骤 1: 更新配置文件日期 ===
[2025-07-30 13:35:22] [INFO] 开始更新配置文件日期...
[2025-07-30 13:35:22] [SUCCESS] 配置文件日期更新成功，目标日期: 2025_07_29
[2025-07-30 13:35:22] [INFO] 成功更新 5/5 个配置文件
[2025-07-30 13:35:22] [INFO] 处理配置文件: depth_current.yaml (合并海流数据)
[2025-07-30 13:35:22] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\config\depth_current.yaml
[2025-07-30 13:35:22] [INFO] 开始处理合并的海流深度数据
[2025-07-30 13:35:22] [INFO] 正在读取 eastward_deep 数据
[2025-07-30 13:35:22] [WARNING] NetCDF文件不存在: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025_07_29\eastward_current_deep.nc
[2025-07-30 13:35:22] [INFO] 正在读取 northward_deep 数据
[2025-07-30 13:35:22] [WARNING] NetCDF文件不存在: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025_07_29\northward_current_deep.nc
[2025-07-30 13:35:22] [INFO] 正在读取 eastward_shallow 数据
[2025-07-30 13:35:22] [WARNING] NetCDF文件不存在: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025_07_29\eastward_current_shallow.nc
[2025-07-30 13:35:22] [INFO] 正在读取 northward_shallow 数据
[2025-07-30 13:35:22] [WARNING] NetCDF文件不存在: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025_07_29\northward_current_shallow.nc
[2025-07-30 13:35:22] [ERROR] 处理配置文件 depth_current.yaml 失败: 只成功读取了 0/4 个数据源，处理中止
[2025-07-30 13:35:22] 
=== 处理完成 ===
[2025-07-30 13:35:22] [INFO] 总共生成 0 个JSON文件
[2025-07-30 13:35:22] [INFO] 完整日志已保存到: D:\GIT\fisherycube-data-process\vector_generator_v0.6\log\nc_to_geojson_202507301335.log
