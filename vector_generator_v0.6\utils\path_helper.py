#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
path_helper.py
--------------
路径辅助模块，处理exe运行时的路径解析
"""

import os
import sys
from pathlib import Path


def get_base_path():
    """
    获取程序的基础路径
    在开发环境下返回脚本所在目录
    在exe环境下返回exe文件所在目录
    
    Returns:
        Path: 基础路径
    """
    if getattr(sys, 'frozen', False):
        # 运行在PyInstaller打包的exe中
        base_path = Path(sys.executable).parent
    else:
        # 运行在开发环境中
        base_path = Path(__file__).parent.parent
    
    return base_path


def get_project_path(path_type: str):
    """
    获取项目中各种路径的通用函数
    
    Args:
        path_type: 路径类型 ('config', 'data', 'output', 'log')
        
    Returns:
        Path: 对应路径
    """
    base_path = get_base_path()
    return base_path / path_type


def get_config_path():
    """获取config目录路径"""
    return get_project_path("config")


def get_data_path():
    """获取data目录路径"""
    return get_project_path("data")


def get_output_path():
    """获取output目录路径"""
    return get_project_path("output")


def get_log_path():
    """获取log目录路径"""
    return get_project_path("log")


def ensure_directories():
    """
    确保必要的目录存在
    """
    directories = [
        get_output_path(),
        get_log_path()
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)


def resolve_path(relative_path):
    """
    解析相对路径为绝对路径
    
    Args:
        relative_path (str or Path): 相对路径
        
    Returns:
        Path: 绝对路径
    """
    base_path = get_base_path()
    return base_path / relative_path


def resolve_file_path(file_path_str):
    """
    智能解析文件路径，支持绝对路径和相对路径
    
    Args:
        file_path_str (str): 文件路径字符串
        
    Returns:
        Path: 解析后的路径对象
    """
    file_path = Path(file_path_str)
    
    # 如果是绝对路径，直接返回
    if file_path.is_absolute():
        return file_path
    
    # 如果是相对路径，相对于项目根目录解析
    base_path = get_base_path()
    resolved_path = base_path / file_path
    
    # 确保路径规范化（解析..和.等）
    return resolved_path.resolve() 