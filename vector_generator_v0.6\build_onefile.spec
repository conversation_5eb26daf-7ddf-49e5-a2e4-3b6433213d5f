# -*- mode: python ; coding: utf-8 -*-
# 单文件打包配置
# 支持全部处理和单个yaml文件处理功能
# 
# 使用方法:
#   VectorGenerator.exe --all                                          # 处理所有配置文件
#   VectorGenerator.exe --config surface_current.yaml                  # 处理海表流场数据
#   VectorGenerator.exe --config depth_current.yaml                    # 处理地转流数据
#   VectorGenerator.exe --config component_of_wind.yaml                # 处理GFS风分量数据
#   VectorGenerator.exe --config 10_metre_wind_component.yaml          # 处理ECMWF风场数据
#   VectorGenerator.exe --config surface_current.yaml --skip-update    # 跳过日期更新
#   VectorGenerator.exe --help                                         # 查看完整帮助信息
#
# 注意：必须指定 --all 或 --config 参数之一

import sys
from pathlib import Path

block_cipher = None
project_root = Path.cwd()

a = Analysis(
    ['nc_to_geojson.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('utils', 'utils'),

    ],
    hiddenimports=[
        # NetCDF4相关
        'netCDF4',
        'netCDF4._netCDF4',
        'netCDF4.utils',
        'cftime',
        'cftime._cftime',
        
        # 核心依赖
        'numpy',
        'numpy.core',
        'numpy.core._multiarray_umath',
        'yaml',
        'h5py',
        'h5py._hl',
        'h5py.h5ac',
        
        # 标准库
        'pathlib',
        'datetime',
        'json',
        'logging',
        'argparse',
        'sys',
        'os',
        'typing',
        
        # utils模块的所有子模块
        'utils',
        'utils.config_manager',
        'utils.data_processor', 
        'utils.custom_logger',
        'utils.path_helper',
        'utils.module',
        'utils.geojson_utils',
        'utils.update_dates',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'scipy',
        'pandas', 
        'xarray',
        'tkinter',
        'PyQt5',
        'PyQt6',
        'PySide2',
        'PySide6',
        'IPython',
        'jupyter',
        # 移除run_nc_reader（已不再使用）
        'run_nc_reader',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='VectorGenerator',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[
        # 排除可能导致压缩问题的文件
        'vcruntime140.dll',
        'python3*.dll',
    ],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 如果有图标文件可以在这里指定
) 