name: component_of_wind
description: GFS风场分量数据处理配置
data:
  date_folder: '2025_07_28'
  data_type: netcdf
  time_configs:
    forecast_times:
    - 24
    - 48
    - 72
    - 96
    - 120
    - 144
    - 168
    time_range:
    - 0
    - 6
    time_labels:
    - 24
    - 48
    - 72
    - 96
    - 120
    - 144
    - 168
  data_sources:
    u_component:
      input_file: ./data/2025_07_28/U_component_of_wind/U_component_of_wind.nc
      variable_name: u10
      description: U分量风速数据
      parameter_number: 2
      parameter_name: U-component_of_wind
    v_component:
      input_file: ./data/2025_07_28/V_component_of_wind/V_component_of_wind.nc
      variable_name: v10
      description: V分量风速数据
      parameter_number: 3
      parameter_name: V-component_of_wind
processing:
  unit_conversion: 1.0
  valid_range:
  - -50.0
  - 50.0
  fill_value: nan
  filter:
    enabled: false
    type: gaussian
    parameters:
      sigma: 0.5
output:
  base_path: ./output/2025_07_28/
  crs: EPSG:4326
  include_value: true
  longitude_range: '0_360'
  convert_to_180: false
  format: json
  separate_timesteps: true
  output_pattern: wind_gfs_10m_{time_label}.json
netcdf:
  coordinate_variables:
    latitude:
    - lat
    - latitude
    - y
    - lat_0
    longitude:
    - lon
    - longitude
    - x
    - lon_0
    time:
    - time
    - forecast_time
  data_processing:
    flatten_method: row_major
    handle_nan: true
    nan_replacement: null
  metadata:
    parameter_unit: m.s-1
    surface_type: 103
    surface_value: 10.0
wind_layer:
  header_template:
    discipline: 0
    disciplineName: Meteorological Products
    gribEdition: 2
    center: 7
    centerName: US National Weather Service - NCEP
    parameterCategory: 2
    parameterUnit: m.s-1
    surface1Type: 103
    surface1TypeName: Specified height level above ground
    surface1Value: 10.0
  multilayer:
    enabled: true
    process_all_timesteps: true
geometry_simplification:
  enabled: false
  method: none
  parameters:
    tolerance: 0
