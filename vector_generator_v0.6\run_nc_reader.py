# -*- coding: utf-8 -*-
"""
Created on Tue Apr 22 11:58:42 2025

@author: zhouw

run_nc_reader.py
----------------
NetCDF文件读取工具，支持从目录中读取NetCDF文件并提取指定变量数据。

★ 使用方法
1. 指定DATA_DIR为NetCDF文件夹路径
2. 选择后端: "xarray"或"netcdf4"
3. 指定要读取的变量名
4. 运行脚本
"""

import sys
from pathlib import Path
import numpy as np
from typing import Dict, List, Any, Union, Optional

# 从utils模块导入所需功能
from utils import (
    identify_coordinate_variables,
    add_coordinate_variables,
    open_nc_file,
    close_nc_file,
    get_variable_data
)

def find_nc_files(directory):
    """查找目录下所有.nc文件"""
    directory = Path(directory).expanduser()
    return list(directory.rglob("*.nc"))

def run(directory: Path, backend: str = "netcdf4", variable_names: list = None, batch: bool = False):
    """
    直接读取目录中的 NetCDF 文件，返回指定多个变量的数据。
    
    参数：
    - directory (Path): NetCDF 文件所在目录
    - backend (str): 使用的后端库 ("xarray" 或 "netcdf4")
    - variable_names (list): 需要提取的多个变量名（如 ['lon', 'lat', 'analysed_sst']）
    - batch (bool): 是否批量读取目录下的所有文件
    """
    # 如果没有传入变量名，默认读取空列表
    if variable_names is None:
        variable_names = []
    
    # 查找所有.nc文件
    nc_files = find_nc_files(directory)
    if not nc_files:
        print(f"警告: 在目录 {directory} 中未找到任何NetCDF文件")
        return {}
        
    # 根据batch参数决定读取模式
    if batch:
        files_to_process = nc_files
    else:
        files_to_process = [nc_files[0]] if nc_files else []
    
    # 用于存储变量数据的字典
    variables_data = {}
    
    # 处理每个文件
    for file_path in files_to_process:
        try:
            print(f"读取文件: {file_path}")
            ds = open_nc_file(file_path, backend)
            
            # 添加经纬度变量到变量名列表
            var_names = add_coordinate_variables(variable_names, ds)
            
            # 获取所有变量的数据
            for var_name in var_names:
                if var_name not in variables_data:  # 避免重复添加相同变量
                    data = get_variable_data(ds, var_name)
                    if data is not None:
                        variables_data[var_name] = data
            
            # 关闭文件
            close_nc_file(ds, backend)
            
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
    
    return variables_data

#%%
if __name__ == "__main__":
    # 配置文件夹路径
    directory = Path("./data/")  # 修改为你的数据目录
    variable_names = ["longitude", "latitude", "u10", "v10"]  # 需要获取的多个变量名
    backend = "netcdf4"  # 使用的读取后端（"xarray" 或 "netcdf4"）
    batch = False  # 是否批量读取

    # 运行并获取数据
    variables_data = run(directory, backend=backend, variable_names=variable_names, batch=batch)

    # 打印结果
    if variables_data:
        print("提取的变量数据：")
        for var_name, data in variables_data.items():
            print(f"{var_name} 数据形状：{data.shape}")
            # 打印数据的一小部分
            preview_size = min(3, data.shape[0])
            print(f"前{preview_size}行数据预览：")
            print(data[:preview_size])
    else:
        print(f"未能提取到任何变量数据。")
