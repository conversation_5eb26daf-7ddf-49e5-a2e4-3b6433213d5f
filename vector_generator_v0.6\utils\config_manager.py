#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
config_manager.py
-----------------
配置文件管理模块，直接导入update_dates避免subprocess问题
"""

import yaml
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Union
from utils.path_helper import get_config_path, get_data_path, get_output_path, resolve_path, resolve_file_path


def update_config_dates(logger):
    """
    直接导入update_dates模块进行日期更新
    避免subprocess调用导致的exe递归问题
    
    Args:
        logger: 日志器实例
    """
    try:
        # 直接导入update_dates模块
        from utils.update_dates import update_all_dates
        
        logger.log_print("[INFO] 开始更新配置文件日期...")
        success_count, total_count, target_date = update_all_dates()
        
        if success_count == total_count:
            logger.log_print(f"[SUCCESS] 配置文件日期更新成功，目标日期: {target_date}")
            logger.log_print(f"[INFO] 成功更新 {success_count}/{total_count} 个配置文件")
        else:
            logger.log_print(f"[WARNING] 部分配置文件更新失败，成功: {success_count}/{total_count}")
            
    except ImportError as e:
        logger.log_print(f"[WARNING] 无法导入update_dates模块: {e}")
        logger.log_print("[INFO] 跳过配置文件日期更新")
    except Exception as e:
        logger.log_print(f"[ERROR] 配置文件日期更新失败: {e}")


def load_yaml_config(config_path: Union[str, Path], logger) -> Dict:
    """
    加载YAML配置文件
    
    Args:
        config_path: 配置文件路径
        logger: 日志器实例
        
    Returns:
        Dict: 配置字典
    """
    config_path = Path(config_path)
    if not config_path.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logger.log_print(f"[INFO] 成功加载配置文件: {config_path}")
        return config
    except Exception as e:
        raise ValueError(f"配置文件解析失败: {e}")


def get_nc_file_path(config: Dict, logger) -> Path:
    """
    根据配置构建NetCDF文件路径
    
    Args:
        config: 配置字典
        logger: 日志器实例
        
    Returns:
        Path: NetCDF文件路径
    """
    data_config = config.get('data', {})
    date_folder = data_config.get('date_folder', '')
    return _build_nc_file_path(data_config, date_folder, logger)


def get_nc_file_path_from_source(source_config: Dict, date_folder: str, logger) -> Path:
    """
    从数据源配置构建NetCDF文件路径
    
    Args:
        source_config: 数据源配置字典
        date_folder: 日期文件夹
        logger: 日志器实例
        
    Returns:
        Path: NetCDF文件路径
    """
    return _build_nc_file_path(source_config, date_folder, logger)


def _build_nc_file_path(config: Dict, date_folder: str, logger) -> Path:
    """
    构建NetCDF文件路径的通用函数
    
    Args:
        config: 配置字典
        date_folder: 日期文件夹
        logger: 日志器实例
        
    Returns:
        Path: NetCDF文件路径
    """
    # 支持新的input_file格式和旧的file_pattern格式
    if 'input_file' in config:
        # 新格式：支持模板变量
        input_file = config['input_file']
        # 使用正则表达式替换日期模板，支持更灵活的匹配
        import re
        input_file = re.sub(r'\d{4}_\d{2}_\d{2}', date_folder, input_file)
        # 使用智能路径解析，支持绝对路径和相对路径
        file_path = resolve_file_path(input_file)
    else:
        # 旧格式：兼容性支持
        file_pattern = config.get('file_pattern', '')
        file_path = get_data_path() / date_folder / file_pattern
    
    if not file_path.exists():
        raise FileNotFoundError(f"NetCDF文件不存在: {file_path}")
    
    logger.log_print(f"[INFO] 找到NetCDF文件: {file_path}")
    return file_path


def extract_model_info(config: Dict, logger) -> Tuple[str, str]:
    """
    从配置中提取模型信息
    
    Args:
        config: 配置字典
        logger: 日志器实例
        
    Returns:
        Tuple[str, str]: (模型名称, 输出前缀)
    """
    wind_layer = config.get('wind_layer', {})
    header_template = wind_layer.get('header_template', {})
    center_name = header_template.get('centerName', '')
    
    if 'European Centre' in center_name:
        model_info = ('ecmwf', 'wind_10m_ecmwf')
    elif 'US National Weather Service' in center_name or 'NCEP' in center_name:
        model_info = ('gfs', 'wind_10m_gfs')
    else:
        # 默认值
        model_info = ('unknown', 'wind_10m')
    
    logger.log_print(f"[INFO] 识别的模型: {model_info[0].upper()}, 输出前缀: {model_info[1]}")
    return model_info


def get_variable_names(config: Dict, logger) -> List[str]:
    """
    根据配置获取需要读取的变量名
    
    Args:
        config: 配置字典
        logger: 日志器实例
        
    Returns:
        List[str]: 变量名列表
    """
    data_config = config.get('data', {})
    
    # 检查是否是合并的风分量文件（包含u10和v10）
    if 'variables' in data_config:
        variables = data_config['variables']
        u_component = variables.get('u_component', 'u10')
        v_component = variables.get('v_component', 'v10')
        var_list = ["longitude", "latitude", u_component, v_component, "valid_time"]
    else:
        # 单一变量文件
        variable_name = data_config.get('variable_name', 'u10')
        var_list = ["longitude", "latitude", variable_name, "valid_time"]
    
    logger.log_print(f"[INFO] 需要读取的变量: {var_list}")
    return var_list


def get_forecast_hours(config: Dict, logger) -> List[int]:
    """
    从配置获取预报时间列表
    
    Args:
        config: 配置字典
        logger: 日志器实例
        
    Returns:
        List[int]: 预报时间列表（小时）
    """
    wind_layer = config.get('wind_layer', {})
    forecast_times = wind_layer.get('forecast_times', [24, 48, 72, 96, 120, 144, 168])
    logger.log_print(f"[INFO] 预报时间列表: {forecast_times}")
    return forecast_times





def get_current_output_config(config: Dict, logger) -> Tuple[Path, str]:
    """
    获取海流数据输出配置信息
    
    Args:
        config: 配置字典
        logger: 日志器实例
        
    Returns:
        Tuple[Path, str]: (输出目录, 输出模式)
    """
    output_config = config.get('output', {})
    data_config = config.get('data', {})
    date_folder = data_config.get('date_folder', '')
    
    output_dir, _ = _get_output_path_from_config(output_config, date_folder, logger)
    output_pattern = output_config.get('deep_pattern', 'depth_current_{depth_label}.json')
    
    logger.log_print(f"[INFO] 海流输出目录: {output_dir}")
    logger.log_print(f"[INFO] 海流输出模式: {output_pattern}")
    return output_dir, output_pattern


def get_surface_current_config(config: Dict, logger) -> Tuple[Path, str]:
    """
    获取海表流场输出配置信息
    
    Args:
        config: 配置字典
        logger: 日志器实例
        
    Returns:
        Tuple[Path, str]: (输出目录, 输出文件名)
    """
    output_config = config.get('output', {})
    data_config = config.get('data', {})
    date_folder = data_config.get('date_folder', '')
    
    if 'json_path' in output_config:
        output_dir, filename = _get_output_from_json_path(output_config, date_folder, logger)
    else:
        # 回退到旧逻辑
        output_dir = get_output_path() / date_folder
        surface_layer = config.get('surface_layer', {})
        filename = surface_layer.get('output_pattern', 'surface_current.json')
        logger.log_print(f"[INFO] 使用默认输出路径")
    
    # 确保输出目录存在
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logger.log_print(f"[INFO] 海表流场输出目录: {output_dir}")
    logger.log_print(f"[INFO] 海表流场输出文件: {filename}")
    return output_dir, filename


def get_output_config(config: Dict, logger) -> Tuple[Path, str, str]:
    """
    获取输出配置信息
    
    Args:
        config: 配置字典
        logger: 日志器实例
        
    Returns:
        Tuple[Path, str, str]: (输出目录, 日期文件夹, 输出前缀)
    """
    output_config = config.get('output', {})
    data_config = config.get('data', {})
    date_folder = data_config.get('date_folder', '')
    
    # 优先使用配置文件中的路径配置
    if 'json_path' in output_config:
        output_dir, filename = _get_output_from_json_path(output_config, date_folder, logger)
        # 从文件名中提取前缀（去掉时间标签部分）
        output_prefix = filename.rsplit('_', 1)[0] if '_' in filename else filename
    elif 'base_path' in output_config:
        output_dir, output_prefix = _get_output_from_base_path(output_config, date_folder, logger)
    else:
        # 回退到旧逻辑
        output_dir = get_output_path() / date_folder
        model_name, output_prefix = extract_model_info(config, logger)
        logger.log_print(f"[INFO] 使用默认输出路径")
    
    # 确保输出目录存在
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logger.log_print(f"[INFO] 输出目录: {output_dir}")
    logger.log_print(f"[INFO] 输出前缀: {output_prefix}")
    return output_dir, date_folder, output_prefix


def get_surface_variable_names(config: Dict, logger) -> Tuple[str, str]:
    """
    获取海表流场变量名
    
    Args:
        config: 配置字典
        logger: 日志器实例
        
    Returns:
        Tuple[str, str]: (U分量变量名, V分量变量名)
    """
    data_config = config.get('data', {})
    variables = data_config.get('variables', {})
    u_component = variables.get('u_component', 'utotal')
    v_component = variables.get('v_component', 'vtotal')
    
    logger.log_print(f"[INFO] 海表流场变量: U分量={u_component}, V分量={v_component}")
    return u_component, v_component


def _get_output_path_from_config(output_config: Dict, date_folder: str, logger) -> Tuple[Path, str]:
    """
    从输出配置中获取输出路径的通用函数
    
    Args:
        output_config: 输出配置字典
        date_folder: 日期文件夹
        logger: 日志器实例
        
    Returns:
        Tuple[Path, str]: (输出目录, 使用的路径类型)
    """
    if 'base_path' in output_config:
        # 使用base_path配置
        base_path = output_config['base_path']
        # 支持模板变量替换
        import re
        base_path = re.sub(r'\d{4}_\d{2}_\d{2}', date_folder, base_path)
        output_dir = Path(base_path)
        logger.log_print(f"[INFO] 使用配置的base_path: {base_path}")
        return output_dir, "base_path"
    elif 'json_path' in output_config:
        output_dir, _ = _get_output_from_json_path(output_config, date_folder, logger)
        return output_dir, "json_path"
    else:
        # 使用默认路径
        output_dir = get_output_path() / date_folder
        logger.log_print(f"[INFO] 使用默认输出路径")
        return output_dir, "default"


def _get_output_from_json_path(output_config: Dict, date_folder: str, logger) -> Tuple[Path, str]:
    """
    从json_path配置中提取输出目录和文件名
    
    Args:
        output_config: 输出配置字典
        date_folder: 日期文件夹
        logger: 日志器实例
        
    Returns:
        Tuple[Path, str]: (输出目录, 文件名)
    """
    json_path = output_config['json_path']
    # 支持模板变量替换
    import re
    json_path = re.sub(r'\d{4}_\d{2}_\d{2}', date_folder, json_path)
    json_path = Path(json_path)
    output_dir = json_path.parent
    filename = json_path.name
    logger.log_print(f"[INFO] 使用配置的json_path: {json_path}")
    return output_dir, filename


def _get_output_from_base_path(output_config: Dict, date_folder: str, logger) -> Tuple[Path, str]:
    """
    从base_path配置中提取输出目录和前缀
    
    Args:
        output_config: 输出配置字典
        date_folder: 日期文件夹
        logger: 日志器实例
        
    Returns:
        Tuple[Path, str]: (输出目录, 输出前缀)
    """
    base_path = output_config['base_path']
    # 支持模板变量替换
    import re
    base_path = re.sub(r'\d{4}_\d{2}_\d{2}', date_folder, base_path)
    output_dir = Path(base_path)
    # 从output_pattern中提取前缀
    output_pattern = output_config.get('output_pattern', 'wind_10m_{time_label}.json')
    output_prefix = output_pattern.split('_{')[0] if '_{' in output_pattern else 'wind_10m'
    logger.log_print(f"[INFO] 使用配置的base_path: {base_path}")
    return output_dir, output_prefix