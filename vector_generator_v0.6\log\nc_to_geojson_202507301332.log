[2025-07-30 13:32:20] === nc_to_geojson.py 开始执行 ===
[2025-07-30 13:32:20] [INFO] 日志文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\log\nc_to_geojson_202507301332.log
[2025-07-30 13:32:20] [INFO] 运行模式: 全部处理
[2025-07-30 13:32:20] 
=== 步骤 1: 更新配置文件日期 ===
[2025-07-30 13:32:20] [WARNING] 无法导入update_dates模块: cannot import name 'update_all_dates' from 'utils.update_dates' (D:\GIT\fisherycube-data-process\vector_generator_v0.6\utils\update_dates.py)
[2025-07-30 13:32:20] [INFO] 跳过配置文件日期更新
[2025-07-30 13:32:20] 
=== 步骤 2: 处理ECMWF数据 ===
[2025-07-30 13:32:20] [INFO] 处理配置文件: 10_metre_wind_component.yaml (ECMWF风场数据)
[2025-07-30 13:32:20] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\config\10_metre_wind_component.yaml
[2025-07-30 13:32:20] [INFO] 开始处理合并的风分量数据
[2025-07-30 13:32:20] [ERROR] 处理配置文件 10_metre_wind_component.yaml 失败: NetCDF文件不存在: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\2025-07-28\10_metre_wind_component\10_metre_wind_component.nc
[2025-07-30 13:32:20] 
=== 步骤 3: 处理GFS数据 ===
[2025-07-30 13:32:20] [INFO] 处理配置文件: component_of_wind.yaml (合并风分量数据)
[2025-07-30 13:32:20] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\config\component_of_wind.yaml
[2025-07-30 13:32:20] [INFO] 开始处理合并的风分量数据
[2025-07-30 13:32:20] [ERROR] 处理配置文件 component_of_wind.yaml 失败: NetCDF文件不存在: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\2025-07-28\U_component_of_wind\U_component_of_wind.nc
[2025-07-30 13:32:20] 
=== 步骤 4: 处理地转流场数据 ===
[2025-07-30 13:32:20] [INFO] 处理配置文件: depth_current.yaml (合并海流数据)
[2025-07-30 13:32:20] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\config\depth_current.yaml
[2025-07-30 13:32:20] [INFO] 开始处理合并的海流深度数据
[2025-07-30 13:32:20] [INFO] 正在读取 eastward_deep 数据
[2025-07-30 13:32:20] [WARNING] NetCDF文件不存在: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-28\eastward_current_deep.nc
[2025-07-30 13:32:20] [INFO] 正在读取 northward_deep 数据
[2025-07-30 13:32:20] [WARNING] NetCDF文件不存在: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-28\northward_current_deep.nc
[2025-07-30 13:32:20] [INFO] 正在读取 eastward_shallow 数据
[2025-07-30 13:32:20] [WARNING] NetCDF文件不存在: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-28\eastward_current_shallow.nc
[2025-07-30 13:32:20] [INFO] 正在读取 northward_shallow 数据
[2025-07-30 13:32:20] [WARNING] NetCDF文件不存在: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-28\northward_current_shallow.nc
[2025-07-30 13:32:20] [ERROR] 处理配置文件 depth_current.yaml 失败: 只成功读取了 0/4 个数据源，处理中止
[2025-07-30 13:32:20] 
=== 步骤 5: 处理海表流场数据 ===
[2025-07-30 13:32:20] [INFO] 处理配置文件: surface_current.yaml (海表流场数据)
[2025-07-30 13:32:20] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\config\surface_current.yaml
[2025-07-30 13:32:20] [INFO] 开始处理海表流场数据...
[2025-07-30 13:32:20] [ERROR] 处理配置文件 surface_current.yaml 失败: NetCDF文件不存在: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\2025-07-28\surface_current\surface_current.nc
[2025-07-30 13:32:20] 
=== 步骤 6: 处理涌浪数据 ===
[2025-07-30 13:32:20] [INFO] 处理配置文件: swell_waves.yaml (涌浪数据)
[2025-07-30 13:32:20] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\config\swell_waves.yaml
[2025-07-30 13:32:20] [INFO] 开始处理涌浪数据...
[2025-07-30 13:32:20] [INFO] 方向文件: data\2025-07-28\direction_of_swell_waves\direction_of_swell_waves.nc
[2025-07-30 13:32:20] [INFO] 周期文件: data\2025-07-28\mean_period_of_total_swell\mean_period_of_total_swell.nc
[2025-07-30 13:32:20] [INFO] 正在读取文件: data\2025-07-28\direction_of_swell_waves\direction_of_swell_waves.nc
[2025-07-30 13:32:20] [ERROR] 读取文件失败: [Errno 2] No such file or directory: 'data\\2025-07-28\\direction_of_swell_waves\\direction_of_swell_waves.nc'
[2025-07-30 13:32:20] [ERROR] 处理配置文件 swell_waves.yaml 失败: 无法读取NC文件数据: data\2025-07-28\direction_of_swell_waves\direction_of_swell_waves.nc - [Errno 2] No such file or directory: 'data\\2025-07-28\\direction_of_swell_waves\\direction_of_swell_waves.nc'
[2025-07-30 13:32:20] 
=== 处理完成 ===
[2025-07-30 13:32:20] [INFO] 总共生成 0 个JSON文件
[2025-07-30 13:32:20] [INFO] 完整日志已保存到: D:\GIT\fisherycube-data-process\vector_generator_v0.6\log\nc_to_geojson_202507301332.log
