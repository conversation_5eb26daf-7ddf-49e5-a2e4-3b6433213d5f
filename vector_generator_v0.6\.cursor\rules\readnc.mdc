---
description: when project need to read NetCDF files, follow the rules
globs: 
alwaysApply: false
---
# NetCDF数据读取规则（项目内utils模式）

## 项目结构规则

1. 每个项目必须包含独立的`utils`文件夹
2. 项目结构应如下所示：

```
project_name/
  ├── utils/
  │   ├── __init__.py
  │   └── module.py
  ├── data/
  └── main_script.py
```

## 基本原则

1. 统一使用项目内的`utils`模块读取NetCDF文件
2. 优先使用netcdf4后端，必要时可切换到xarray后端
3. 始终处理经纬度坐标变量
4. 封装异常处理，确保程序稳定运行

## 详细规则

### 1. 项目初始化规则

```python
# 每个项目都需要导入本项目的utils模块
from utils import (
    identify_coordinate_variables,
    add_coordinate_variables,
    open_nc_file,
    close_nc_file,
    get_variable_data
)

# 也可以使用直接运行函数
from utils import run
```

### 2. 文件读取规则

```python
from pathlib import Path
# 从当前项目的utils导入模块
from utils import open_nc_file, close_nc_file, get_variable_data

# 1. 构建文件路径
file_path = Path("./data/your_file.nc")

# 2. 使用指定后端打开文件
ds = open_nc_file(file_path, backend="netcdf4")

# 3. 读取完成后必须关闭文件
try:
    # 进行数据处理
    pass
finally:
    close_nc_file(ds, backend="netcdf4")
```

### 3. 变量提取规则

```python
# 从当前项目的utils导入
from utils import add_coordinate_variables, get_variable_data

# 1. 定义需要提取的变量
variable_names = ["analysed_sst"]  # 明确指定业务变量

# 2. 自动添加经纬度变量
var_names = add_coordinate_variables(variable_names, ds)

# 3. 获取所有变量数据
variables_data = {}
for var_name in var_names:
    data = get_variable_data(ds, var_name)
    if data is not None:
        variables_data[var_name] = data
```

### 4. 完整读取流程

```python
from pathlib import Path
# 使用项目内的utils模块
from utils import run

# 配置参数
directory = Path("./data")  # 数据目录
variable_names = ["lon", "lat", "analysed_sst"]  # 需要获取的变量
backend = "netcdf4"  # 使用的后端
batch = False  # 是否批量处理

# 执行读取
variables_data = run(directory, backend=backend, variable_names=variable_names, batch=batch)
```

## 跨项目一致性维护

1. 每创建新项目时，复制完整的`utils`文件夹到项目根目录
2. 确保utils内的文件保持一致（`__init__.py`和`module.py`）
3. 不要修改utils中的核心功能，确保所有项目使用相同的读取逻辑
4. 如需扩展功能，可在项目中创建额外的辅助模块，不要修改原有utils

## 最佳实践

1. 使用相对导入确保代码在任何项目中都能正常工作
2. 项目初始化时即完成utils模块的复制和配置
3. 当核心读取逻辑需要更新时，同步更新所有项目中的utils模块
4. 为确保一致性，可以使用版本号标记utils模块的版本
5. 在每个项目中保持数据文件夹结构一致，推荐使用`./data`目录

通过以上规则，可以在不同项目中保持NetCDF数据读取方法的一致性，同时允许每个项目拥有独立的utils模块，避免跨项目依赖。


