#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
YAML配置文件日期更新工具（简化版）
批量更新config目录下所有YAML文件中的日期信息
作者：中星海洋
"""

import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
import yaml
import re


# ===========================================
# 配置参数加载
# ===========================================

def load_update_config():
    """
    加载日期更新配置文件
    
    Returns:
        dict: 配置参数字典
    """
    config_path = Path('./config/update/update.yaml')
    
    # 默认配置
    default_config = {
        'date_mode': 'days_ago',
        'days_ago': 1,
        'specific_date': '2025_06_19',
        'config_dir': './config',
        'verbose': False,
        'settings': {
            'auto_create_output_dir': True,
            'backup_config_files': False,
            'backup_dir': './backup',
            'date_format': '%Y_%m_%d',
            'config_extensions': ['*.yaml', '*.yml']
        }
    }
    
    # 尝试加载配置文件
    if config_path.exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = yaml.safe_load(f) or {}
            
            # 合并配置（用户配置覆盖默认配置）
            config = default_config.copy()
            config.update(user_config)
            
            # 处理嵌套的settings配置
            if 'settings' in user_config:
                config['settings'].update(user_config['settings'])
            
            print(f"[SUCCESS] 已加载配置文件: {config_path}")
            return config
            
        except Exception as e:
            print(f"[WARNING] 配置文件加载失败，使用默认配置: {e}")
            return default_config
    else:
        print(f"[INFO] 配置文件不存在，使用默认配置: {config_path}")
        return default_config

# 加载配置
CONFIG = load_update_config()

# 向后兼容的全局变量（保持原有代码工作）
DATE_MODE = CONFIG['date_mode']
DAYS_AGO = CONFIG['days_ago']
SPECIFIC_DATE = CONFIG['specific_date']
CONFIG_DIR = CONFIG['config_dir']
VERBOSE = CONFIG['verbose']

# ===========================================


def get_target_date():
    """
    根据配置参数获取目标日期字符串
    
    Returns:
        str: YYYY_MM_DD格式的日期字符串
    """
    if DATE_MODE == 'today':
        return datetime.now().strftime('%Y_%m_%d')
    elif DATE_MODE == 'days_ago':
        target_date = datetime.now() - timedelta(days=DAYS_AGO)
        return target_date.strftime('%Y_%m_%d')
    elif DATE_MODE == 'specific':
        # 验证日期格式
        if not re.match(r'^\d{4}_\d{2}_\d{2}$', SPECIFIC_DATE):
            raise ValueError(f"日期格式错误，应为YYYY_MM_DD格式: {SPECIFIC_DATE}")
        # 验证日期有效性
        try:
            datetime.strptime(SPECIFIC_DATE, '%Y_%m_%d')
        except ValueError:
            raise ValueError(f"无效的日期: {SPECIFIC_DATE}")
        return SPECIFIC_DATE
    else:
        raise ValueError(f"不支持的日期模式: {DATE_MODE}")


def update_yaml_dates(yaml_path, new_date):
    """
    更新单个YAML文件中的日期信息
    
    Args:
        yaml_path: YAML文件路径
        new_date: 新的日期字符串(YYYY_MM_DD格式)
    
    Returns:
        bool: 是否成功更新
    """
    try:
        # 读取YAML文件
        with open(yaml_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        
        if not data:
            if VERBOSE:
                print(f"  [WARNING] 文件为空或格式错误: {yaml_path}")
            return False
        
        # 记录原始值
        old_date_folder = data.get('data', {}).get('date_folder', 'N/A')
        old_input_file = data.get('data', {}).get('input_file', 'N/A')
        old_base_path = data.get('output', {}).get('base_path', 'N/A')
        old_geojson_path = data.get('output', {}).get('geojson_path', 'N/A')
        old_shapefile_path = data.get('output', {}).get('shapefile_path', 'N/A')
        old_json_path = data.get('output', {}).get('json_path', 'N/A')
        
        # 更新date_folder
        if 'data' not in data:
            data['data'] = {}
        data['data']['date_folder'] = new_date
        
        # 更新input_file中的日期模板
        if 'input_file' in data.get('data', {}):
            old_input = data['data']['input_file']
            if isinstance(old_input, str):
                # 使用正则表达式匹配并替换日期模板（支持多种格式）
                new_input = re.sub(r'\d{4}_\d{2}_\d{2}', new_date, old_input)
                data['data']['input_file'] = new_input
        
        # 更新data_sources中的input_file（如果存在）
        if 'data_sources' in data.get('data', {}):
            for source_name, source_config in data['data']['data_sources'].items():
                if 'input_file' in source_config:
                    old_source_input = source_config['input_file']
                    new_source_input = re.sub(r'\d{4}_\d{2}_\d{2}', new_date, old_source_input)
                    source_config['input_file'] = new_source_input
        
        # 更新input_file中的嵌套配置（如涌浪数据）
        if 'input_file' in data.get('data', {}) and isinstance(data['data']['input_file'], dict):
            for key, config in data['data']['input_file'].items():
                if isinstance(config, dict) and 'input_file' in config:
                    old_nested_input = config['input_file']
                    if isinstance(old_nested_input, str):
                        new_nested_input = re.sub(r'\d{4}_\d{2}_\d{2}', new_date, old_nested_input)
                        config['input_file'] = new_nested_input
        
        # 更新输出路径
        if 'output' not in data:
            data['output'] = {}
        
        path_updated = False
        
        # 更新base_path
        if 'base_path' in data['output']:
            old_base = data['output']['base_path']
            new_base = re.sub(r'\d{4}_\d{2}_\d{2}', new_date, old_base)
            data['output']['base_path'] = new_base
            path_updated = True
        
        # 更新geojson_path
        old_geojson = data['output'].get('geojson_path', '')
        if old_geojson:
            # 提取文件名部分
            path_obj = Path(old_geojson)
            filename = path_obj.name
            # 构造新的路径
            new_path = f"./output/{new_date}/{filename}"
            data['output']['geojson_path'] = new_path
            path_updated = True
        
        # 更新shapefile_path
        old_shapefile = data['output'].get('shapefile_path', '')
        if old_shapefile:
            # 提取文件名部分
            path_obj = Path(old_shapefile)
            filename = path_obj.name
            # 构造新的路径
            new_path = f"./output/{new_date}/{filename}"
            data['output']['shapefile_path'] = new_path
            path_updated = True
        
        # 更新json_path（Wind Layer配置文件专用）
        old_json = data['output'].get('json_path', '')
        if old_json:
            # 提取文件名部分
            path_obj = Path(old_json)
            filename = path_obj.name
            # 构造新的路径
            new_path = f"./output/{new_date}/{filename}"
            data['output']['json_path'] = new_path
            path_updated = True
        
        # 至少要有一个路径被更新，或者只更新了date_folder也算成功
        if not path_updated:
            if VERBOSE:
                print(f"  [INFO] 只更新了date_folder字段: {yaml_path.name}")
            # 只要更新了date_folder就算成功
            path_updated = True
        
        # 写回YAML文件
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(data, f, default_flow_style=False, allow_unicode=True, 
                     sort_keys=False, indent=2)
        
        if VERBOSE:
            print(f"  [SUCCESS] 更新成功: {yaml_path.name}")
            print(f"     date_folder: {old_date_folder} -> {new_date}")
            if old_input_file != 'N/A' and 'input_file' in data.get('data', {}):
                print(f"     input_file: {old_input_file} -> {data['data']['input_file']}")
            if old_base_path != 'N/A' and 'base_path' in data.get('output', {}):
                print(f"     base_path: {old_base_path} -> {data['output']['base_path']}")
            if old_geojson and 'geojson_path' in data['output']:
                print(f"     geojson_path: {old_geojson_path} -> {data['output']['geojson_path']}")
            if old_shapefile and 'shapefile_path' in data['output']:
                print(f"     shapefile_path: {old_shapefile_path} -> {data['output']['shapefile_path']}")
            if old_json and 'json_path' in data['output']:
                print(f"     json_path: {old_json_path} -> {data['output']['json_path']}")
        
        return True
        
    except Exception as e:
        if VERBOSE:
            print(f"  [ERROR] 更新失败 {yaml_path}: {e}")
        return False


def find_yaml_files(config_dir):
    """
    查找config目录下的所有YAML文件
    
    Args:
        config_dir: config目录路径
    
    Returns:
        list: YAML文件路径列表
    """
    config_path = Path(config_dir)
    if not config_path.exists():
        raise FileNotFoundError(f"配置目录不存在: {config_dir}")
    
    # 使用配置文件中定义的扩展名
    extensions = CONFIG.get('settings', {}).get('config_extensions', ['*.yaml', '*.yml'])
    yaml_files = []
    
    for ext in extensions:
        yaml_files.extend(list(config_path.glob(ext)))
    
    # 排除update目录下的文件和示例文件
    yaml_files = [f for f in yaml_files if 'update' not in f.parts and 'example' not in f.name.lower()]
    
    if not yaml_files:
        raise FileNotFoundError(f"在{config_dir}目录下未找到YAML文件")
    
    return sorted(yaml_files)


def update_all_dates():
    """
    更新所有YAML配置文件中的日期
    
    Returns:
        tuple: (成功数量, 总数量, 目标日期)
    """
    try:
        # 获取目标日期
        target_date = get_target_date()
        
        # 查找YAML文件
        yaml_files = find_yaml_files(CONFIG_DIR)
        
        if VERBOSE:
            print(f"[TARGET] 目标日期: {target_date}")
            print(f"[FILE] 配置目录: {CONFIG_DIR}")
            print(f"[LOG] 找到 {len(yaml_files)} 个YAML文件")
            print("=" * 60)
        
        # 执行更新
        success_count = 0
        for yaml_file in yaml_files:
            if VERBOSE:
                print(f"[EDIT] 处理文件: {yaml_file.name}")
            if update_yaml_dates(yaml_file, target_date):
                success_count += 1
            if VERBOSE:
                print()
        
        return success_count, len(yaml_files), target_date
        
    except Exception as e:
        if VERBOSE:
            print(f"[ERROR] 执行出错: {e}")
        return 0, 0, None


def main():
    """主函数"""
    success_count, total_count, target_date = update_all_dates()
    
    if VERBOSE:
        print("=" * 60)
        if target_date:
            print(f"[CELEBRATION] 更新完成！成功更新 {success_count}/{total_count} 个文件到日期: {target_date}")
        
        if success_count < total_count:
            print("[WARNING] 部分文件更新失败，请检查上述错误信息")
            sys.exit(1)
        else:
            print("[SUCCESS] 所有文件更新成功！")
    
    return success_count == total_count


if __name__ == '__main__':
    main() 