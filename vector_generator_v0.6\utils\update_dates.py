#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
YAML配置文件日期更新工具（重构版）
批量更新config目录下所有YAML文件中的日期信息，支持多种日期格式
作者：中星海洋
"""

import re
import sys
from datetime import datetime, timedelta
from pathlib import Path
import yaml


class DateUpdater:
    """日期更新器 - 支持多种日期格式的智能更新"""

    # 支持的日期格式模式（按优先级排序）
    DATE_PATTERNS = [
        (r'\d{4}_\d{2}_\d{2}', '%Y_%m_%d'),      # 2025_07_29
        (r'\d{4}-\d{2}-\d{2}', '%Y-%m-%d'),      # 2025-07-29
        (r'\d{4}/\d{2}/\d{2}', '%Y/%m/%d'),      # 2025/07/29
        (r'\d{8}', '%Y%m%d'),                    # 20250729
    ]

    def __init__(self, config_path='./config/update/update.yaml'):
        """初始化日期更新器"""
        self.config = self._load_config(config_path)
        self.verbose = self.config.get('verbose', False)

    def _load_config(self, config_path):
        """加载配置文件"""
        default_config = {
            'date_mode': 'days_ago',
            'days_ago': 1,
            'specific_date': '2025_07_29',
            'config_dir': './config',
            'verbose': False,
            'settings': {
                'config_extensions': ['*.yaml', '*.yml']
            }
        }

        config_file = Path(config_path)
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = yaml.safe_load(f) or {}
                default_config.update(user_config)
                # 使用配置中的verbose设置来决定是否输出日志
                if default_config.get('verbose', False):
                    print(f"[SUCCESS] 已加载配置文件: {config_path}")
            except Exception as e:
                print(f"[WARNING] 配置文件加载失败，使用默认配置: {e}")
        else:
            # 使用配置中的verbose设置来决定是否输出日志
            if default_config.get('verbose', False):
                print(f"[INFO] 配置文件不存在，使用默认配置: {config_path}")

        return default_config

    def get_target_date(self, format_pattern='%Y_%m_%d'):
        """获取目标日期字符串"""
        mode = self.config['date_mode']

        if mode == 'today':
            target_date = datetime.now()
        elif mode == 'days_ago':
            days = self.config.get('days_ago', 1)
            target_date = datetime.now() - timedelta(days=days)
        elif mode == 'specific':
            date_str = self.config.get('specific_date', '2025_07_29')
            # 自动检测输入日期格式
            for pattern, fmt in self.DATE_PATTERNS:
                if re.match(f'^{pattern}$', date_str):
                    target_date = datetime.strptime(date_str, fmt)
                    break
            else:
                raise ValueError(f"无法识别的日期格式: {date_str}")
        else:
            raise ValueError(f"不支持的日期模式: {mode}")

        return target_date.strftime(format_pattern)

    def _update_value_recursive(self, obj, old_date_pattern, new_date):
        """递归更新对象中的所有日期值"""
        if isinstance(obj, dict):
            for key, value in obj.items():
                obj[key] = self._update_value_recursive(value, old_date_pattern, new_date)
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                obj[i] = self._update_value_recursive(item, old_date_pattern, new_date)
        elif isinstance(obj, str):
            # 检查并替换所有支持的日期格式
            for pattern, _ in self.DATE_PATTERNS:
                obj = re.sub(pattern, new_date, obj)

        return obj


    def update_yaml_file(self, yaml_path, new_date):
        """更新单个YAML文件中的所有日期"""
        try:
            # 读取YAML文件
            with open(yaml_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)

            if not data:
                if self.verbose:
                    print(f"  [WARNING] 文件为空或格式错误: {yaml_path}")
                return False

            # 备份原始数据用于比较
            original_data = yaml.dump(data, default_flow_style=False)

            # 递归更新所有日期值
            updated_data = self._update_value_recursive(data, None, new_date)

            # 检查是否有更新
            updated_yaml = yaml.dump(updated_data, default_flow_style=False)
            if original_data == updated_yaml:
                if self.verbose:
                    print(f"  [INFO] 无需更新: {yaml_path.name}")
                return True

            # 写回YAML文件
            with open(yaml_path, 'w', encoding='utf-8') as f:
                yaml.dump(updated_data, f, default_flow_style=False,
                         allow_unicode=True, sort_keys=False, indent=2)

            if self.verbose:
                print(f"  [SUCCESS] 更新成功: {yaml_path.name}")

            return True

        except Exception as e:
            if self.verbose:
                print(f"  [ERROR] 更新失败 {yaml_path}: {e}")
            return False


    def find_yaml_files(self):
        """查找config目录下的所有YAML文件"""
        config_dir = self.config.get('config_dir', './config')
        config_path = Path(config_dir)

        if not config_path.exists():
            raise FileNotFoundError(f"配置目录不存在: {config_dir}")

        # 获取支持的文件扩展名
        extensions = self.config.get('settings', {}).get('config_extensions', ['*.yaml', '*.yml'])
        yaml_files = []

        for ext in extensions:
            yaml_files.extend(list(config_path.glob(ext)))

        # 排除update目录下的文件和示例文件
        yaml_files = [f for f in yaml_files
                     if 'update' not in f.parts and 'example' not in f.name.lower()]

        if not yaml_files:
            raise FileNotFoundError(f"在{config_dir}目录下未找到YAML文件")

        return sorted(yaml_files)

    def update_all_files(self):
        """更新所有YAML配置文件中的日期"""
        try:
            # 获取目标日期
            target_date = self.get_target_date()

            # 查找YAML文件
            yaml_files = self.find_yaml_files()

            if self.verbose:
                print(f"[TARGET] 目标日期: {target_date}")
                print(f"[FILE] 配置目录: {self.config.get('config_dir', './config')}")
                print(f"[LOG] 找到 {len(yaml_files)} 个YAML文件")
                print("=" * 60)

            # 执行更新
            success_count = 0
            for yaml_file in yaml_files:
                if self.verbose:
                    print(f"[EDIT] 处理文件: {yaml_file.name}")
                if self.update_yaml_file(yaml_file, target_date):
                    success_count += 1
                if self.verbose:
                    print()

            return success_count, len(yaml_files), target_date

        except Exception as e:
            if self.verbose:
                print(f"[ERROR] 执行出错: {e}")
            return 0, 0, None


def main():
    """主函数"""
    # 创建日期更新器实例
    updater = DateUpdater()

    # 执行更新
    success_count, total_count, target_date = updater.update_all_files()

    if updater.verbose:
        print("=" * 60)
        if target_date:
            print(f"[CELEBRATION] 更新完成！成功更新 {success_count}/{total_count} 个文件到日期: {target_date}")

        if success_count < total_count:
            print("[WARNING] 部分文件更新失败，请检查上述错误信息")
            sys.exit(1)
        else:
            print("[SUCCESS] 所有文件更新成功！")

    return success_count == total_count


if __name__ == '__main__':
    main()