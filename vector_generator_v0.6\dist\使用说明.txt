================================================================================
                    VectorGenerator v0.3 使用说明
================================================================================

【程序功能】
  将NetCDF格式的海洋气象数据转换为GeoJSON格式，支持：
  - ECMWF风场数据 (7个时间文件)
  - GFS风场数据 (7个时间文件)  
  - 地转流数据 (31个深度文件)
  - 海表流场数据 (1个表面文件)

【使用步骤】
  1. 将NetCDF数据文件放入 data/日期文件夹/ 对应目录
  2. 命令行运行（必须使用命令行，双击运行将显示帮助）
     方式一：VectorGenerator.exe --all (处理全部)
     方式二：VectorGenerator.exe --config xxx.yaml (处理单个)
  3. 等待处理完成
  4. 在 output/日期文件夹/ 查看生成的JSON文件

【命令行使用】
  # 处理所有配置文件
  VectorGenerator.exe --all
  
  # 处理指定配置文件
  VectorGenerator.exe --config surface_current.yaml      (海表流场)
  VectorGenerator.exe --config deep_current.yaml         (地转流)
  VectorGenerator.exe --config 10_metre_wind_component.yaml  (ECMWF风场)
  VectorGenerator.exe --config component_of_wind.yaml     (GFS风场)
  
  # 跳过日期更新
  VectorGenerator.exe --config surface_current.yaml --skip-update
  
  # 查看帮助
  VectorGenerator.exe --help

【目录结构】
  VectorGenerator.exe  - 主程序
  config/              - 配置文件 (自动更新日期)
  data/                - 输入数据目录
  output/              - 输出结果目录  
  log/                 - 运行日志目录

【数据文件结构】
  data/2025_06_26/
  ├── 10_metre_wind_component/     (ECMWF风场)
  ├── U_component_of_wind/         (GFS U分量)
  ├── V_component_of_wind/         (GFS V分量)
  ├── eastward_current_deep/       (海流东向深层)
  ├── northward_current_deep/      (海流北向深层)
  ├── eastward_current_shallow/    (海流东向浅层)
  ├── northward_current_shallow/   (海流北向浅层)
  └── surface_current/             (海表流场)

【输出文件】
  wind_10m_ecmwf_24.json    - ECMWF 24小时预报
  wind_10m_ecmwf_48.json    - ECMWF 48小时预报
  ... (共7个ECMWF文件)
  
  wind_10m_gfs_24.json      - GFS 24小时预报  
  wind_10m_gfs_48.json      - GFS 48小时预报
  ... (共7个GFS文件)
  
  depth_current_0.json      - 0米深度海流(浅层)
  depth_current_2.json      - 2米深度海流(浅层)
  ... (共21个浅层文件)
  depth_current_78.json     - 78米深度海流(深层)
  depth_current_92.json     - 92米深度海流(深层)
  ... (共11个深层文件)
  
  surface_current.json      - 海表面流场

【注意事项】
  ✓ 程序会自动更新配置文件日期（可用--skip-update跳过）
  ✓ 数据自动从1/12°降采样到1/4°分辨率
  ✓ 输出标准GeoJSON格式，包含速度、方向、时间、深度信息
  ✓ 运行日志保存在log目录，便于问题排查
  ✓ 单独处理可节省时间和系统资源
  ✓ 海流深度处理较慢（32个文件），风场处理较快（7个文件）


【技术参数】
  输入格式: NetCDF4
  输出格式: GeoJSON
  坐标系统: WGS84
  时间格式: ISO 8601
  处理方式: 批量自动化
