# -*- coding: utf-8 -*-
"""
module.py
---------
NetCDF文件读取工具的辅助模块，包含了经纬度变量识别和后端处理功能。
"""

from pathlib import Path
from typing import Any, Dict, List, Tuple, Optional, Union
import numpy as np
from datetime import datetime


def identify_coordinate_variables(ds) -> Tuple[Optional[str], Optional[str]]:
    """
    识别数据集中的经纬度变量名。
    
    参数：
    - ds: xarray.Dataset 或 netCDF4.Dataset 对象
    
    返回：
    - tuple: (经度变量名, 纬度变量名)，如果未找到则对应位置返回 None
    """
    # 可能的经度变量名列表（按优先级排序）
    lon_variants = [
        'longitude',
        'lon',
        'lons',
        'long',
        'Longitude',
        'LONGITUDE',
        'LON',
        'LONS',
        'x'  # 某些数据集使用 x 表示经度
    ]
    
    # 可能的纬度变量名列表（按优先级排序）
    lat_variants = [
        'latitude',
        'lat',
        'lats',
        'Latitude',
        'LATITUDE',
        'LAT',
        'LATS',
        'y'  # 某些数据集使用 y 表示纬度
    ]
    
    found_lon = None
    found_lat = None
    
    # 获取数据集中的所有变量名
    if hasattr(ds, 'variables'):
        if hasattr(ds.variables, 'keys') and callable(getattr(ds.variables, 'keys')):
            variables = ds.variables.keys()
        else:
            variables = list(ds.variables)
        
        # 查找经度变量
        for lon_name in lon_variants:
            if lon_name in variables:
                found_lon = lon_name
                break
                
        # 查找纬度变量
        for lat_name in lat_variants:
            if lat_name in variables:
                found_lat = lat_name
                break
    
    return found_lon, found_lat


def add_coordinate_variables(variable_names: list, ds) -> list:
    """
    向变量名列表中添加经纬度变量（如果尚未包含）。
    
    参数：
    - variable_names: 原始变量名列表
    - ds: 数据集对象
    
    返回：
    - list: 包含经纬度变量名的新列表
    """
    if variable_names is None:
        variable_names = []
    
    lon_name, lat_name = identify_coordinate_variables(ds)
    
    # 添加经度变量（如果存在且尚未包含）
    if lon_name and lon_name not in variable_names:
        variable_names.append(lon_name)
    
    # 添加纬度变量（如果存在且尚未包含）
    if lat_name and lat_name not in variable_names:
        variable_names.append(lat_name)
    
    return variable_names


def open_nc_file(path: Union[str, Path], backend: str = "netcdf4"):
    """
    打开NetCDF文件，仅支持netCDF4后端以减少依赖。
    
    参数:
    - path: NetCDF文件路径
    - backend: 使用的后端（固定为 "netcdf4"）
    
    返回:
    - 数据集对象
    """
    try:
        path = Path(path)
        # 只使用netCDF4后端，减少依赖和打包大小
        from netCDF4 import Dataset
        return Dataset(path, mode="r")
    except Exception as e:
        print(f"打开文件 {path} 时出错: {e}")
        raise


def close_nc_file(ds, backend: str = "netcdf4"):
    """
    关闭NetCDF文件，释放资源。
    
    参数:
    - ds: 数据集对象
    - backend: 使用的后端 ("xarray" 或 "netcdf4")
    """
    if ds is not None:
        try:
            ds.close()
        except Exception as e:
            print(f"关闭数据集时出错: {e}")


def get_variable_data(ds, variable_name: str) -> Any:
    """
    从数据集中提取指定变量的数据。
    
    参数:
    - ds: 数据集对象
    - variable_name: 变量名
    
    返回:
    - 变量数据，如不存在则返回None
    """
    if hasattr(ds, 'variables') and variable_name in ds.variables:
        try:
            return ds[variable_name][:]
        except Exception as e:
            print(f"读取变量 {variable_name} 时出错: {e}")
            return None
    else:
        print(f"警告：变量 '{variable_name}' 不存在于该文件中。")
        return None


def calculate_wind_vectors(u: np.ndarray, v: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
    """
    计算风向和风速
    
    Args:
        u (np.ndarray): 东西风分量 (m/s)
        v (np.ndarray): 南北风分量 (m/s)
        
    Returns:
        Tuple[np.ndarray, np.ndarray]: (风速, 风向角度)
        风向角度：0°为北风（从北向南吹），90°为东风，180°为南风，270°为西风
    """
    # 计算风速（速度标量）
    speed = np.sqrt(u**2 + v**2)
    
    # 计算风向（角度）
    # arctan2给出的是数学角度，需要转换为气象角度
    # 数学角度：0°指向东，逆时针为正
    # 气象角度：0°指向北，顺时针为正
    deg = (270 - np.rad2deg(np.arctan2(v, u))) % 360
    
    return speed, deg


def convert_unix_to_iso(unix_seconds: float) -> str:
    """
    将Unix时间戳转换为ISO格式字符串
    
    Args:
        unix_seconds (float): Unix时间戳（秒）
        
    Returns:
        str: ISO格式的时间字符串
    """
    return datetime.utcfromtimestamp(unix_seconds).isoformat() + "Z"


def is_valid_value(value):
    """
    检查值是否有效（非NaN、非masked、非None）
    
    Args:
        value: 要检查的值
        
    Returns:
        bool: 值是否有效
    """
    try:
        # 检查是否为masked值
        if hasattr(value, 'mask') and value.mask:
            return False
        # 检查是否为NaN
        if np.isnan(float(value)):
            return False
        # 检查是否为None
        if value is None:
            return False
        return True
    except (TypeError, ValueError):
        return False


def safe_float_convert(value, default=None):
    """
    安全地将值转换为float，处理NaN和masked值
    
    Args:
        value: 要转换的值
        default: 如果转换失败返回的默认值
        
    Returns:
        float或default: 转换后的值或默认值
    """
    try:
        # 检查是否为masked值
        if hasattr(value, 'mask') and value.mask:
            return default
        
        # 转换为float
        float_val = float(value)
        
        # 检查是否为NaN
        if np.isnan(float_val):
            return default
            
        return float_val
    except (TypeError, ValueError):
        return default


def create_point_features(
    lons: np.ndarray,
    lats: np.ndarray,
    speeds: np.ndarray,
    directions: np.ndarray,
    time: float,
    depth_value: Optional[float] = None
) -> List[Dict]:
    """
    创建GeoJSON Point特征列表
    
    Args:
        lons (np.ndarray): 经度数组
        lats (np.ndarray): 纬度数组
        speeds (np.ndarray): 风速或海流速度数组 (m/s)
        directions (np.ndarray): 风向或海流方向数组 (度)
        time (float): Unix时间戳（秒）
        depth_value (Optional[float]): 深度值（米），用于海流数据
        
    Returns:
        List[Dict]: 符合RFC 7946规范的GeoJSON特征列表
    """
    features = []
    
    # 确保所有数组形状相同
    if not (lons.shape == lats.shape == speeds.shape == directions.shape):
        raise ValueError("所有输入数组的形状必须相同")
    
    # 遍历所有网格点
    for i in range(lons.shape[0]):
        for j in range(lons.shape[1]):
            # 安全转换所有值
            lon_val = safe_float_convert(lons[i, j])
            lat_val = safe_float_convert(lats[i, j])
            speed_val = safe_float_convert(speeds[i, j])
            dir_val = safe_float_convert(directions[i, j])
            
            # 跳过任何无效值
            if any(val is None for val in [lon_val, lat_val, speed_val, dir_val]):
                continue
                
            # 创建属性字典
            properties = {
                "speed": round(speed_val, 2),      # 速度保留2位小数
                "deg": round(dir_val, 1),          # 方向保留1位小数
                "time": int(time)                  # Unix时间戳（整数秒）
            }
            
            # 如果有深度值，添加到属性中
            if depth_value is not None:
                depth_val = safe_float_convert(depth_value)
                if depth_val is not None:
                    properties["depth"] = round(depth_val, 1)
                
            # 创建GeoJSON Point特征
            feature = {
                "type": "Feature",
                "geometry": {
                    "type": "Point",
                    "coordinates": [
                        round(lon_val, 4),  # 经度
                        round(lat_val, 4)   # 纬度
                    ]
                },
                "properties": properties
            }
            features.append(feature)
    
    return features
