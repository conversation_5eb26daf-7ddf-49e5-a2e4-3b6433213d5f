[2025-07-30 11:56:48] === nc_to_geojson.py 开始执行 ===
[2025-07-30 11:56:48] [INFO] 日志文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\log\nc_to_geojson_202507301156.log
[2025-07-30 11:56:48] [INFO] 运行模式: 单文件处理 (depth_current.yaml)
[2025-07-30 11:56:48] 
=== 步骤 1: 更新配置文件日期 ===
[2025-07-30 11:56:48] [INFO] 开始更新配置文件日期...
[2025-07-30 11:56:48] [SUCCESS] 配置文件日期更新成功，目标日期: 2025_07_29
[2025-07-30 11:56:48] [INFO] 成功更新 5/5 个配置文件
[2025-07-30 11:56:48] [INFO] 处理配置文件: depth_current.yaml (合并海流数据)
[2025-07-30 11:56:48] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\config\depth_current.yaml
[2025-07-30 11:56:48] [INFO] 开始处理合并的海流深度数据
[2025-07-30 11:56:48] [INFO] 正在读取 eastward_deep 数据
[2025-07-30 11:56:48] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\eastward_current_deep.nc
[2025-07-30 11:56:48] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\eastward_current_deep.nc
[2025-07-30 11:56:48] [SUCCESS] NetCDF文件数据读取成功
[2025-07-30 11:56:48] [INFO] 正在读取 northward_deep 数据
[2025-07-30 11:56:48] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\northward_current_deep.nc
[2025-07-30 11:56:48] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\northward_current_deep.nc
[2025-07-30 11:56:49] [SUCCESS] NetCDF文件数据读取成功
[2025-07-30 11:56:49] [INFO] 正在读取 eastward_shallow 数据
[2025-07-30 11:56:49] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\eastward_current_shallow.nc
[2025-07-30 11:56:49] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\eastward_current_shallow.nc
[2025-07-30 11:56:49] [SUCCESS] NetCDF文件数据读取成功
[2025-07-30 11:56:49] [INFO] 正在读取 northward_shallow 数据
[2025-07-30 11:56:49] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\northward_current_shallow.nc
[2025-07-30 11:56:49] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\liming01\2025-07-29\northward_current_shallow.nc
[2025-07-30 11:56:50] [SUCCESS] NetCDF文件数据读取成功
[2025-07-30 11:56:50] [INFO] 处理deep层海流数据...
[2025-07-30 11:56:50] [INFO] 数据维度 - 经度: 1801, 纬度: 721, 深度: 11
[2025-07-30 11:56:50] [INFO] 原始海流数据维度 - uo: (1, 11, 721, 1801), vo: (1, 11, 721, 1801)
[2025-07-30 11:56:50] [INFO] 开始降采样海流数据...
[2025-07-30 11:56:50] [INFO] 降采样: (1, 11, 721, 1801) -> (1, 11, 241, 601)
[2025-07-30 11:56:50] [INFO] 降采样: (1, 11, 721, 1801) -> (1, 11, 241, 601)
[2025-07-30 11:56:50] [INFO] 处理深度层 1/11 - 77.85385131835938m (标签: 78)
[2025-07-30 11:56:51] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:56:51] [SUCCESS] 已生成文件: depth_current_78.json (包含 132242 个数据点)
[2025-07-30 11:56:51] [INFO] 处理深度层 2/11 - 92.3260726928711m (标签: 92)
[2025-07-30 11:56:53] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:56:53] [SUCCESS] 已生成文件: depth_current_92.json (包含 132098 个数据点)
[2025-07-30 11:56:53] [INFO] 处理深度层 3/11 - 109.72930145263672m (标签: 110)
[2025-07-30 11:56:55] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:56:55] [SUCCESS] 已生成文件: depth_current_110.json (包含 131940 个数据点)
[2025-07-30 11:56:55] [INFO] 处理深度层 4/11 - 130.66600036621094m (标签: 131)
[2025-07-30 11:56:57] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:56:57] [SUCCESS] 已生成文件: depth_current_131.json (包含 131799 个数据点)
[2025-07-30 11:56:57] [INFO] 处理深度层 5/11 - 155.85069274902344m (标签: 156)
[2025-07-30 11:56:59] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:56:59] [SUCCESS] 已生成文件: depth_current_156.json (包含 131646 个数据点)
[2025-07-30 11:56:59] [INFO] 处理深度层 6/11 - 186.12559509277344m (标签: 186)
[2025-07-30 11:57:01] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:01] [SUCCESS] 已生成文件: depth_current_186.json (包含 131516 个数据点)
[2025-07-30 11:57:01] [INFO] 处理深度层 7/11 - 222.47520446777344m (标签: 222)
[2025-07-30 11:57:02] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:03] [SUCCESS] 已生成文件: depth_current_222.json (包含 131372 个数据点)
[2025-07-30 11:57:03] [INFO] 处理深度层 8/11 - 266.0403137207031m (标签: 266)
[2025-07-30 11:57:04] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:04] [SUCCESS] 已生成文件: depth_current_266.json (包含 131273 个数据点)
[2025-07-30 11:57:04] [INFO] 处理深度层 9/11 - 318.1274108886719m (标签: 318)
[2025-07-30 11:57:06] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:06] [SUCCESS] 已生成文件: depth_current_318.json (包含 131184 个数据点)
[2025-07-30 11:57:06] [INFO] 处理深度层 10/11 - 380.2130126953125m (标签: 380)
[2025-07-30 11:57:08] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:08] [SUCCESS] 已生成文件: depth_current_380.json (包含 131090 个数据点)
[2025-07-30 11:57:08] [INFO] 处理深度层 11/11 - 453.9377136230469m (标签: 454)
[2025-07-30 11:57:10] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:10] [SUCCESS] 已生成文件: depth_current_454.json (包含 130948 个数据点)
[2025-07-30 11:57:10] [INFO] 处理shallow层海流数据...
[2025-07-30 11:57:10] [INFO] 数据维度 - 经度: 1801, 纬度: 721, 深度: 21
[2025-07-30 11:57:10] [INFO] 原始海流数据维度 - uo: (1, 21, 721, 1801), vo: (1, 21, 721, 1801)
[2025-07-30 11:57:10] [INFO] 开始降采样海流数据...
[2025-07-30 11:57:10] [INFO] 降采样: (1, 21, 721, 1801) -> (1, 21, 241, 601)
[2025-07-30 11:57:10] [INFO] 降采样: (1, 21, 721, 1801) -> (1, 21, 241, 601)
[2025-07-30 11:57:10] [INFO] 处理深度层 1/21 - 0.49402499198913574m (标签: 0)
[2025-07-30 11:57:11] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:12] [SUCCESS] 已生成文件: depth_current_0.json (包含 134068 个数据点)
[2025-07-30 11:57:12] [INFO] 处理深度层 2/21 - 1.5413750410079956m (标签: 2)
[2025-07-30 11:57:13] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:13] [SUCCESS] 已生成文件: depth_current_2.json (包含 134068 个数据点)
[2025-07-30 11:57:13] [INFO] 处理深度层 3/21 - 2.6456689834594727m (标签: 3)
[2025-07-30 11:57:15] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:15] [SUCCESS] 已生成文件: depth_current_3.json (包含 134068 个数据点)
[2025-07-30 11:57:15] [INFO] 处理深度层 4/21 - 3.8194949626922607m (标签: 4)
[2025-07-30 11:57:17] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:17] [SUCCESS] 已生成文件: depth_current_4.json (包含 134068 个数据点)
[2025-07-30 11:57:17] [INFO] 处理深度层 5/21 - 5.078224182128906m (标签: 5)
[2025-07-30 11:57:19] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:19] [SUCCESS] 已生成文件: depth_current_5.json (包含 134068 个数据点)
[2025-07-30 11:57:19] [INFO] 处理深度层 6/21 - 6.440614223480225m (标签: 6)
[2025-07-30 11:57:21] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:21] [SUCCESS] 已生成文件: depth_current_6.json (包含 134068 个数据点)
[2025-07-30 11:57:21] [INFO] 处理深度层 7/21 - 7.92956018447876m (标签: 8)
[2025-07-30 11:57:22] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:23] [SUCCESS] 已生成文件: depth_current_8.json (包含 133792 个数据点)
[2025-07-30 11:57:23] [INFO] 处理深度层 8/21 - 9.572997093200684m (标签: 10)
[2025-07-30 11:57:24] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:25] [SUCCESS] 已生成文件: depth_current_10.json (包含 133739 个数据点)
[2025-07-30 11:57:25] [INFO] 处理深度层 9/21 - 11.404999732971191m (标签: 11)
[2025-07-30 11:57:26] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:27] [SUCCESS] 已生成文件: depth_current_11.json (包含 133676 个数据点)
[2025-07-30 11:57:27] [INFO] 处理深度层 10/21 - 13.467140197753906m (标签: 14)
[2025-07-30 11:57:28] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:28] [SUCCESS] 已生成文件: depth_current_14.json (包含 133614 个数据点)
[2025-07-30 11:57:28] [INFO] 处理深度层 11/21 - 15.810070037841797m (标签: 16)
[2025-07-30 11:57:30] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:30] [SUCCESS] 已生成文件: depth_current_16.json (包含 133556 个数据点)
[2025-07-30 11:57:30] [INFO] 处理深度层 12/21 - 18.495559692382812m (标签: 19)
[2025-07-30 11:57:32] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:32] [SUCCESS] 已生成文件: depth_current_19.json (包含 133455 个数据点)
[2025-07-30 11:57:32] [INFO] 处理深度层 13/21 - 21.598819732666016m (标签: 22)
[2025-07-30 11:57:34] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:34] [SUCCESS] 已生成文件: depth_current_22.json (包含 133344 个数据点)
[2025-07-30 11:57:34] [INFO] 处理深度层 14/21 - 25.211410522460938m (标签: 25)
[2025-07-30 11:57:36] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:36] [SUCCESS] 已生成文件: depth_current_25.json (包含 133232 个数据点)
[2025-07-30 11:57:36] [INFO] 处理深度层 15/21 - 29.444730758666992m (标签: 29)
[2025-07-30 11:57:38] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:38] [SUCCESS] 已生成文件: depth_current_29.json (包含 133134 个数据点)
[2025-07-30 11:57:38] [INFO] 处理深度层 16/21 - 34.43415069580078m (标签: 34)
[2025-07-30 11:57:40] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:40] [SUCCESS] 已生成文件: depth_current_34.json (包含 133022 个数据点)
[2025-07-30 11:57:40] [INFO] 处理深度层 17/21 - 40.344051361083984m (标签: 40)
[2025-07-30 11:57:42] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:42] [SUCCESS] 已生成文件: depth_current_40.json (包含 132908 个数据点)
[2025-07-30 11:57:42] [INFO] 处理深度层 18/21 - 47.37369155883789m (标签: 47)
[2025-07-30 11:57:43] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:44] [SUCCESS] 已生成文件: depth_current_47.json (包含 132768 个数据点)
[2025-07-30 11:57:44] [INFO] 处理深度层 19/21 - 55.76428985595703m (标签: 56)
[2025-07-30 11:57:45] [INFO] 计算的边界框: [140.0, -40.0, 290.0, 20.0]
[2025-07-30 11:57:46] [SUCCESS] 已生成文件: depth_current_56.json (包含 132612 个数据点)
[2025-07-30 11:57:46] [INFO] 处理深度层 20/21 - 65.80726623535156m (标签: 66)
