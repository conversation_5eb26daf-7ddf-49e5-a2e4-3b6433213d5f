[2025-07-30 13:43:40] === nc_to_geojson.py 开始执行 ===
[2025-07-30 13:43:40] [INFO] 日志文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\log\nc_to_geojson_202507301343.log
[2025-07-30 13:43:40] [INFO] 运行模式: 全部处理
[2025-07-30 13:43:40] 
=== 步骤 1: 更新配置文件日期 ===
[2025-07-30 13:43:40] [INFO] 开始更新配置文件日期...
[2025-07-30 13:43:40] [SUCCESS] 配置文件日期更新成功，目标日期: 2025_07_29
[2025-07-30 13:43:40] [INFO] 成功更新 5/5 个配置文件
[2025-07-30 13:43:40] 
=== 步骤 2: 处理ECMWF数据 ===
[2025-07-30 13:43:40] [INFO] 处理配置文件: 10_metre_wind_component.yaml (ECMWF风场数据)
[2025-07-30 13:43:40] [INFO] 成功加载配置文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\config\10_metre_wind_component.yaml
[2025-07-30 13:43:40] [INFO] 开始处理合并的风分量数据
[2025-07-30 13:43:40] [INFO] 找到NetCDF文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\2025_07_29\10_metre_wind_component\10_metre_wind_component.nc
[2025-07-30 13:43:40] [INFO] 需要读取的变量: ['longitude', 'latitude', 'u10', 'v10', 'valid_time']
[2025-07-30 13:43:40] [INFO] 正在读取文件: D:\GIT\fisherycube-data-process\vector_generator_v0.6\data\2025_07_29\10_metre_wind_component\10_metre_wind_component.nc
[2025-07-30 13:43:40] [SUCCESS] NetCDF文件数据读取成功
[2025-07-30 13:43:40] [INFO] 数据维度 - 经度: 1440, 纬度: 721
[2025-07-30 13:43:40] [INFO] 时间步: 7
[2025-07-30 13:43:40] [INFO] 使用配置的base_path: ./output/2025_07_29/
[2025-07-30 13:43:40] [INFO] 输出目录: output\2025_07_29
[2025-07-30 13:43:40] [INFO] 输出前缀: wind_ecmwf_10m
[2025-07-30 13:43:40] [INFO] 预报时间列表: [24, 48, 72, 96, 120, 144, 168]
[2025-07-30 13:43:40] [INFO] 处理时间步 1/7
[2025-07-30 13:43:40] [INFO] 时间: 2025-07-23T12:00:00Z
