# Vector Generator v0.6

海洋气象数据转换工具，将NetCDF格式数据转换为GeoJSON格式，支持ECMWF/GFS风场数据、海流数据和波浪数据处理。

## 项目功能

- **风场数据处理**：支持ECMWF和GFS风场数据（u10/v10分量）
- **海流数据处理**：支持深层海流和海表流场数据（多深度层）
- **波浪数据处理**：支持波高、波向、波周期等波浪参数
- **数据转换**：NetCDF → GeoJSON，计算速度和方向
- **时间序列处理**：按预报时间和深度层生成独立文件
- **降采样支持**：自动将高分辨率数据降采样至目标分辨率

## 项目结构

```
vector_generator_v0.6/
├── config/                          # YAML配置文件
│   ├── 10_metre_wind_component.yaml # ECMWF风场配置
│   ├── component_of_wind.yaml       # GFS风场配置
│   ├── depth_current.yaml           # 深层海流配置
│   ├── surface_current.yaml         # 海表流场配置
│   ├── swell_waves.yaml             # 波浪数据配置
│   └── update/
│       └── update.yaml               # 日期更新配置
├── data/                            # NetCDF数据文件
│   └── {date}/                      # 按日期组织的数据目录
│       ├── 10_metre_wind_component/ # ECMWF风场数据
│       ├── U_component_of_wind/     # GFS U分量数据
│       ├── V_component_of_wind/     # GFS V分量数据
│       ├── eastward_current_deep/   # 深层海流东向分量
│       ├── northward_current_deep/  # 深层海流北向分量
│       ├── surface_current/         # 海表流场数据
│       └── direction_of_swell_waves/ # 波浪数据
├── utils/                           # 工具模块
│   ├── config_manager.py            # 配置管理
│   ├── data_processor.py            # 数据处理
│   ├── custom_logger.py             # 日志系统
│   ├── geojson_utils.py             # GeoJSON生成
│   ├── module.py                    # NetCDF读取和计算
│   ├── path_helper.py               # 路径管理
│   └── update_dates.py              # 日期更新
├── output/                          # 输出JSON文件
│   └── {date}/                      # 按日期组织的输出目录
├── log/                             # 处理日志
├── nc_to_geojson.py                 # 主处理脚本
├── run_nc_reader.py                 # NetCDF读取工具
└── requirements.txt                 # Python依赖
```

## 使用方法

### 基本使用

1. **准备数据**：将NetCDF文件放入对应的data目录结构中
2. **配置检查**：确认YAML配置文件中的路径和参数正确
3. **执行处理**：
```bash
python nc_to_geojson.py --all                                   # 处理所有配置文件
python nc_to_geojson.py --config 10_metre_wind_component.yaml   # 处理ECMWF风场数据
python nc_to_geojson.py --config component_of_wind.yaml         # 处理GFS风分量数据
python nc_to_geojson.py --config depth_current.yaml             # 处理地转流数据
python nc_to_geojson.py --config surface_current.yaml           # 处理海表流场数据
python nc_to_geojson.py --config swell_waves.yaml               # 处理涌浪数据
```